16:50:43.047  9494  9494 E AndroidRuntime: FATAL EXCEPTION: main
16:50:43.047  9494  9494 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 9494
16:50:43.047  9494  9494 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:50:43.047  9494  9494 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:50:43.047  9494  9494 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:50:43.047  9494  9494 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:50:43.047  9494  9494 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:50:43.047  9494  9494 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:50:43.047  9494  9494 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:50:43.047  9494  9494 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:50:43.047  9494  9494 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:25.233  9642  9642 D AndroidRuntime: Shutting down VM
16:51:25.234  9642  9642 E AndroidRuntime: FATAL EXCEPTION: main
16:51:25.234  9642  9642 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 9642
16:51:25.234  9642  9642 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:25.234  9642  9642 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:25.234  9642  9642 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:25.234  9642  9642 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:25.234  9642  9642 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:25.234  9642  9642 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:25.234  9642  9642 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:25.234  9642  9642 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:25.234  9642  9642 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:27.789 10871 10871 D AndroidRuntime: Shutting down VM
16:51:27.790 10871 10871 E AndroidRuntime: FATAL EXCEPTION: main
16:51:27.790 10871 10871 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 10871
16:51:27.790 10871 10871 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke interface method 'java.lang.Object com.kaolafm.opensdk.http.core.IRepositoryManager.obtainRetrofitService(java.lang.Class)' on a null object reference
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.kaolafm.opensdk.api.AbstractRequest.obtainRetrofitService(AbstractRequest.java:65)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.kaolafm.opensdk.api.operation.OperationRequest.<init>(OperationRequest.java:45)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getSubcategoryList(YTDataHelper.java:334)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getProvincesCitiesCategoryList(YTDataHelper.java:211)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getProvincesCitiesCategoryList(NetRadioManager.java:125)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:147)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment.initData(BroadcastListFragment.java:688)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.fawcar.eco.commonsdk.basemodule.BaseFragment.onViewCreated(BaseFragment.java:50)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performViewCreated(Fragment.java:3128)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:552)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:261)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:27.790 10871 10871 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:27.790 10871 10871 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:27.790 10871 10871 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:27.790 10871 10871 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:27.790 10871 10871 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:27.790 10871 10871 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:27.790 10871 10871 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:27.790 10871 10871 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:32.670 11005 11005 D AndroidRuntime: Shutting down VM
16:51:32.670 11005 11005 E AndroidRuntime: FATAL EXCEPTION: main
16:51:32.670 11005 11005 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 11005
16:51:32.670 11005 11005 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke interface method 'java.lang.Object com.kaolafm.opensdk.http.core.IRepositoryManager.obtainRetrofitService(java.lang.Class)' on a null object reference
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.kaolafm.opensdk.api.AbstractRequest.obtainRetrofitService(AbstractRequest.java:65)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.kaolafm.opensdk.api.search.SearchRequest.<init>(SearchRequest.java:29)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.searchBroadcastListByKeyword(YTDataHelper.java:425)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.searchBroadcastListByKeyword(NetRadioManager.java:139)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.searchBroadcastListByKeyword(BroadcastListViewModel.java:205)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment.search(BroadcastListFragment.java:704)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment.initData(BroadcastListFragment.java:685)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.fawcar.eco.commonsdk.basemodule.BaseFragment.onViewCreated(BaseFragment.java:50)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performViewCreated(Fragment.java:3128)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:552)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:261)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:32.670 11005 11005 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:32.670 11005 11005 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:32.670 11005 11005 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:32.670 11005 11005 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:32.670 11005 11005 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:32.670 11005 11005 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:32.670 11005 11005 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:32.670 11005 11005 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:35.217 11222 11222 D AndroidRuntime: Shutting down VM
16:51:35.218 11222 11222 E AndroidRuntime: FATAL EXCEPTION: main
16:51:35.218 11222 11222 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 11222
16:51:35.218 11222 11222 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke interface method 'java.lang.Object com.kaolafm.opensdk.http.core.IRepositoryManager.obtainRetrofitService(java.lang.Class)' on a null object reference
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.kaolafm.opensdk.api.AbstractRequest.obtainRetrofitService(AbstractRequest.java:65)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.kaolafm.opensdk.api.operation.OperationRequest.<init>(OperationRequest.java:45)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getSubcategoryList(YTDataHelper.java:334)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getProvincesCitiesCategoryList(YTDataHelper.java:211)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getProvincesCitiesCategoryList(NetRadioManager.java:125)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:147)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment.initData(BroadcastListFragment.java:688)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.fawcar.eco.commonsdk.basemodule.BaseFragment.onViewCreated(BaseFragment.java:50)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performViewCreated(Fragment.java:3128)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.createView(FragmentStateManager.java:552)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:261)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:35.218 11222 11222 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:35.218 11222 11222 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:35.218 11222 11222 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:35.218 11222 11222 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:35.218 11222 11222 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:35.218 11222 11222 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:35.218 11222 11222 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:35.218 11222 11222 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:49.502 11340 11340 D AndroidRuntime: Shutting down VM
16:51:49.503 11340 11340 E AndroidRuntime: FATAL EXCEPTION: main
16:51:49.503 11340 11340 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 11340
16:51:49.503 11340 11340 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:49.503 11340 11340 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:49.503 11340 11340 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:49.503 11340 11340 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:49.503 11340 11340 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:49.503 11340 11340 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:49.503 11340 11340 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:49.503 11340 11340 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:49.503 11340 11340 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:51:57.981 11816 11816 D AndroidRuntime: Shutting down VM
16:51:57.982 11816 11816 E AndroidRuntime: FATAL EXCEPTION: main
16:51:57.982 11816 11816 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 11816
16:51:57.982 11816 11816 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:51:57.982 11816 11816 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:51:57.982 11816 11816 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:51:57.982 11816 11816 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:51:57.982 11816 11816 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:51:57.982 11816 11816 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:51:57.982 11816 11816 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:51:57.982 11816 11816 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:51:57.982 11816 11816 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:52:02.970 12320 12320 D AndroidRuntime: Shutting down VM
16:52:02.970 12320 12320 E AndroidRuntime: FATAL EXCEPTION: main
16:52:02.970 12320 12320 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 12320
16:52:02.970 12320 12320 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:52:02.970 12320 12320 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:52:02.970 12320 12320 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:52:02.970 12320 12320 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:52:02.970 12320 12320 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:52:02.970 12320 12320 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:52:02.970 12320 12320 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:52:02.970 12320 12320 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:52:02.970 12320 12320 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:52:05.194 12440 12440 D AndroidRuntime: Shutting down VM
16:52:05.195 12440 12440 E AndroidRuntime: FATAL EXCEPTION: main
16:52:05.195 12440 12440 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 12440
16:52:05.195 12440 12440 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:52:05.195 12440 12440 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:52:05.195 12440 12440 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:52:05.195 12440 12440 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:52:05.195 12440 12440 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:52:05.195 12440 12440 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:52:05.195 12440 12440 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:52:05.195 12440 12440 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:52:05.195 12440 12440 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
16:53:17.228 14436 14436 D AndroidRuntime: Shutting down VM
16:53:17.228 14436 14436 E AndroidRuntime: FATAL EXCEPTION: main
16:53:17.228 14436 14436 E AndroidRuntime: Process: com.fawcar.ecosystem.netradio, PID: 14436
16:53:17.228 14436 14436 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'void com.kaolafm.opensdk.account.profile.AbstractProfileManager.setLongitude(java.lang.String)' on a null object reference
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.kaolafm.opensdk.BaseEngine.setLocation(BaseEngine.java:110)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.kaolafm.opensdk.KradioSDKInternalEngine.setLocation(KradioSDKInternalEngine.java:170)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDKEngine.setLocation(OpenSDKEngine.java:173)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.kaolafm.opensdk.OpenSDK.setLocation(OpenSDK.java:183)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.YTDataHelper.getLocalBroadcastList(YTDataHelper.java:201)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.fawcar.ecosystem.netradiomanager.NetRadioManager.getLocalBroadcastList(NetRadioManager.java:119)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.fawcar.ecosystem.BroadcastListViewModel.getBroadcastOrSubCategoryList(BroadcastListViewModel.java:128)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:649)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.fawcar.ecosystem.netradio.fragment.BroadcastListFragment$12.onChanged(BroadcastListFragment.java:641)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LiveData.considerNotify(LiveData.java:133)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LiveData.dispatchingValue(LiveData.java:146)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LiveData$ObserverWrapper.activeStateChanged(LiveData.java:483)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LiveData$LifecycleBoundObserver.onStateChanged(LiveData.java:440)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.forwardPass(LifecycleRegistry.jvm.kt:257)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:293)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.Fragment.performStart(Fragment.java:3167)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.start(FragmentStateManager.java:588)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentStateManager.moveToExpectedState(FragmentStateManager.java:279)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.executeOpsTogether(FragmentManager.java:1899)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.removeRedundantOperationsAndExecute(FragmentManager.java:1817)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager.execPendingActions(FragmentManager.java:1760)
16:53:17.228 14436 14436 E AndroidRuntime: 	at androidx.fragment.app.FragmentManager$5.run(FragmentManager.java:547)
16:53:17.228 14436 14436 E AndroidRuntime: 	at android.os.Handler.handleCallback(Handler.java:942)
16:53:17.228 14436 14436 E AndroidRuntime: 	at android.os.Handler.dispatchMessage(Handler.java:99)
16:53:17.228 14436 14436 E AndroidRuntime: 	at android.os.Looper.loopOnce(Looper.java:201)
16:53:17.228 14436 14436 E AndroidRuntime: 	at android.os.Looper.loop(Looper.java:288)
16:53:17.228 14436 14436 E AndroidRuntime: 	at android.app.ActivityThread.main(ActivityThread.java:7918)
16:53:17.228 14436 14436 E AndroidRuntime: 	at java.lang.reflect.Method.invoke(Native Method)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
16:53:17.228 14436 14436 E AndroidRuntime: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:946)
