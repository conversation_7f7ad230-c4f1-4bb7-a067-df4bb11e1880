package com.kaolafm.opensdk.demo.live.ui;

import android.arch.lifecycle.Lifecycle;
import android.arch.lifecycle.LifecycleObserver;
import android.arch.lifecycle.LifecycleOwner;
import android.arch.lifecycle.OnLifecycleEvent;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

//import com.chinanetcenter.wcs.android.entity.OperationMessage;
//import com.chinanetcenter.wcs.android.internal.UploadFileRequest;
//import com.chinanetcenter.wcs.android.listener.FileUploaderListener;
import com.kaolafm.opensdk.api.live.LiveRequest;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.live.model.ChatRoomTokenDetail;
import com.kaolafm.opensdk.demo.live.chat.ChatUserInfo;
import com.kaolafm.opensdk.demo.live.chat.MessageBean;
import com.kaolafm.opensdk.demo.live.chat.NimManager;
import com.kaolafm.opensdk.demo.live.chat.RecordUploadHelper;
import com.kaolafm.opensdk.demo.live.chat.RecorderStatus;
import com.kaolafm.opensdk.demo.live.play.ErrorStatus;
import com.kaolafm.opensdk.demo.live.play.LiveManager;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.netease.nimlib.sdk.NIMClient;
import com.netease.nimlib.sdk.RequestCallback;
import com.netease.nimlib.sdk.auth.LoginInfo;
import com.netease.nimlib.sdk.chatroom.ChatRoomService;
import com.netease.nimlib.sdk.chatroom.model.ChatRoomInfo;
import com.netease.nimlib.sdk.chatroom.model.EnterChatRoomResultData;

import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;

public class LivePresenter implements LifecycleObserver {

    protected LiveIView mView;

    public static final String TAG = "LivePresenter";
    public static final boolean DEBUG_LIVE = true;

    private NimManager.RoomMemberChangedObserver mMemberChangedObserver;
    private NimManager.OnChatMessageReceivedListener mMessageReceivedListener;
    private NimManager.EnterChatRoomListener mEnterChatRoomListener;

    public LivePresenter(LiveIView view) {
        mView = view;
    }

    public void start() {
        if (mView != null && mView instanceof LifecycleOwner) {
            ((LifecycleOwner) mView).getLifecycle().addObserver(this);
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    void onDestroy(LifecycleOwner owner) {
        owner.getLifecycle().removeObserver(this);
    }


    public void getLiveInfo(long programid) {
        if (DEBUG_LIVE) {
            Log.d(TAG, "getShowingInfo programid: " + programid);
        }

        LiveRequest request = new LiveRequest();
        request.getLiveInfo(String.valueOf(programid), new HttpCallback<LiveInfoDetail>() {
            @Override
            public void onSuccess(LiveInfoDetail liveInfo) {
                if (liveInfo != null) {
                    if (mView != null) {
                        mView.showLiveInfo(liveInfo);
                    }
                } else {
                    if (mView != null) {
                        mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (DEBUG_LIVE) {
                    Log.d(TAG, "getShowingInfo onError throwable: " + exception.getMessage());
                }
                if (mView != null) {
                    mView.showErrorInfo(ErrorStatus.ERROR_REQUEST);
                }
            }
        });
    }

    /**
     * 注册云信IM账号
     */
    public void registerToNim(String roomId, NimManager.EnterChatRoomListener listener) {
        if (DEBUG_LIVE) {
            Log.d(TAG, "registerToNim");
        }
        HttpCallback<ChatRoomTokenDetail> callback = new HttpCallback<ChatRoomTokenDetail>() {
            @Override
            public void onSuccess(ChatRoomTokenDetail result) {
                if (result != null && result.getToken() != null && !TextUtils.isEmpty(result.getAccid())) {
                    String accountId = result.getAccid();
                    String token = result.getToken();
                    String nickName = result.getNickName();
                    if (DEBUG_LIVE) {
                        Log.d(TAG, "registerToNim onSuccess accountId: " + accountId
                                + ", token: " + token + ", nickName: " + nickName);
                    }
                    NimManager.getInstance().setAccount(accountId);
                    NimManager.getInstance().setNickName(nickName);
                    LoginInfo loginInfo = new LoginInfo(accountId, token);
                    NimManager.getInstance().startLoginToNim(loginInfo, roomId, listener);
                } else {
                    if (listener != null) {
                        listener.loginFailed(-1);
                    }
                }
            }

            @Override
            public void onError(ApiException t) {
                if (DEBUG_LIVE) {
                    Log.d(TAG, "registerToNim onError", t);
                }
                if (listener != null) {
                    listener.getAccountFailed(0);
                }
            }
        };
        String uid = UserInfoManager.getInstance().getUserId();
        String phone = UserInfoManager.getInstance().getPhone();
        String nickName = UserInfoManager.getInstance().getNickName();
        String avatar = UserInfoManager.getInstance().getAvatar();
        LiveRequest request = new LiveRequest();
        if (UserInfoManager.getInstance().isUsePhone()) {
            request.getChatRoomToken(uid, phone, callback);
        }else {
            request.getChatRoomToken(uid, avatar, nickName, callback);
        }
    }

    public void getListenerNumber(String roomId) {
        NIMClient.getService(ChatRoomService.class).fetchRoomInfo(roomId).setCallback(new RequestCallback<ChatRoomInfo>() {
            @Override
            public void onSuccess(ChatRoomInfo param) {
                if (mView != null && param != null) {
                    mView.showListenerNumber(param.getOnlineUserCount());
                }
            }

            @Override
            public void onFailed(int code) {
            }

            @Override
            public void onException(Throwable exception) {
            }
        });

    }

    public void uploadRecord(Context context, RecordUploadHelper.UploadParam program, String fileName) {
        String path = LiveManager.getInstance().getFilePath();
        if (path == null) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            if (mView != null) {
                mView.showFileNotExist();
            }
            return;
        }
        LiveManager.getInstance().setRecorderStatus(RecorderStatus.UPLOADING);
//        RecordUploadHelper.uploadToWangsu(context, path, program, new RecordUploadListener(),
//                fileName);
    }

//    private class RecordUploadListener extends FileUploaderListener {
//        @Override
//        public void onSuccess(int status, JSONObject responseJson) {
//            if (DEBUG_LIVE) {
//                Log.d(TAG, "onSuccess responseJson : " + responseJson.toString());
//            }
//            if (mView != null) {
//                mView.showRecordUploadSuccess();
//            }
//        }
//
//        @Override
//        public void onFailure(OperationMessage operationMessage) {
//            if (DEBUG_LIVE) {
//                Log.d(TAG, "onFailure errorMessage : " + operationMessage.getMessage());
//            }
//            if (mView != null) {
//                mView.showRecordUploadFailure();
//            }
//        }
//
//        @Override
//        public void onProgress(UploadFileRequest request, long currentSize, long totalSize) {
//            float progress = (totalSize > 0) ? ((float) currentSize / totalSize) * 100 : -1;
//            if (DEBUG_LIVE) {
//                Log.d(TAG, String.format("onProgress Progress %d from %d (%f)",
//                        currentSize, totalSize, progress));
//            }
//            if (mView != null) {
//                mView.showRecordUploadProgress((int) progress);
//            }
//        }
//    }

    public void enterChatRoom(Context context, String roomId) {
        if (DEBUG_LIVE) {
            Log.d(TAG, "enterChatRoom roomId: " + roomId);
        }
        NimManager.getInstance().addRoomMemberChangedObserver(createRoomMemberChangedObserver());
        NimManager.getInstance().addChatMessageReceivedListener(createChatMessageReceivedListener());
        NimManager.getInstance().enterChatRoom(context, roomId, createEnterChatRoomListener());
    }

    public void exitChatRoom() {
        NimManager.getInstance().exitChatRoom();
    }

    public void sendEmptyTextMessage(String roomId, String content) {
        NimManager.SendMsgListener listener = new NimManager.SendMsgListener() {
            @Override
            public void onSendSuccess(MessageBean messageBean) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "SendMsgListener onSendSuccess");
                }
            }

            @Override
            public void onSendFailed(MessageBean messageBean) {
                if (LivePresenter.DEBUG_LIVE) {
                    Log.d(TAG, "SendMsgListener onSendFailed");
                }
            }
        };
        NimManager.SendChatMsgData msgData = new NimManager.SendChatMsgData();
        msgData.content = content;
        msgData.sessionId = roomId;
        NimManager.getInstance().sendTextChatMsg(msgData, listener);
    }

    private NimManager.RoomMemberChangedObserver createRoomMemberChangedObserver() {
        if (mMemberChangedObserver != null) {
            return mMemberChangedObserver;
        }
        mMemberChangedObserver = new NimManager.RoomMemberChangedObserver() {
            @Override
            public void onRoomMemberIn(ChatUserInfo chatUserInfo) {
                if (mView != null) {
                    mView.showRoomMemberEnter(chatUserInfo);
                }
            }

            @Override
            public void onRoomMemberExit(ChatUserInfo chatUserInfo) {

            }
        };
        return mMemberChangedObserver;
    }

    private NimManager.OnChatMessageReceivedListener createChatMessageReceivedListener() {
        if (mMessageReceivedListener != null) {
            return mMessageReceivedListener;
        }
        mMessageReceivedListener = new NimManager.OnChatMessageReceivedListener() {
            @Override
            public void onChatMessageReceived(ArrayList<MessageBean> messageData) {
                if (mView != null) {
                    mView.showChatMessageReceived(messageData);
                }
            }
        };
        return mMessageReceivedListener;
    }

    private NimManager.EnterChatRoomListener createEnterChatRoomListener() {
        if (mEnterChatRoomListener == null) {
            mEnterChatRoomListener = new NimManager.EnterChatRoomListener() {
                @Override
                public void onException(Throwable throwable) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom onException: ", throwable);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                }

                @Override
                public void loginFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom loginFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                }

                @Override
                public void loginSuccess(String account) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom loginSuccess account: " + account);
                    }
                    //此处登录成功，进入聊天室是否成功还需要进一步确认
                }

                @Override
                public void enterChatRoomSuccess(EnterChatRoomResultData data) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom enterChatRoomSuccess account: " + data);
                    }
                    NimManager.getInstance().setChatRoomEntered(true);
                }

                @Override
                public void enterChatRoomFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom enterChatRoomFailed code: " + code);
                    }
                }

                @Override
                public void getAccountFailed(int code) {
                    if (LivePresenter.DEBUG_LIVE) {
                        Log.d(TAG, "enterChatRoom getAccountFailed code: " + code);
                    }
                    NimManager.getInstance().setChatRoomEntered(false);
                }
            };
        }
        return mEnterChatRoomListener;
    }

    public void destroy() {
        mView = null;
    }
}
