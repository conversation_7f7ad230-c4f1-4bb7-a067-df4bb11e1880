package com.kaolafm.report.event;


import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.PlayReportParameter;
import com.kaolafm.report.util.ReportConstants;

/**
 * <AUTHOR> on 2019/1/21.
 * 收听结束
 */

public class EndListenReportEvent extends BaseReportEventBean {
    /**
     * 1.当前通过qqapi接口获取的内容（排行榜等）单曲id为qqapi
     * 2.QQ音乐电台（一人一首招牌歌等）中单曲，上报的audioid 为 qqradio
     */
    private String audioid;
    /**
     * 1.通过api接口获得qq音乐内容，radioid为空
     * 2.qq音乐电台中单曲的radio为qq音乐电台的id
     */
    private String radioid;
    /**
     * 专辑同radioid；电台流为当前播放单曲所属专辑的id
     * qq api接口获取内容，为空
     * qq音乐电台的单曲，为空
     */
    private String albumid;

    /**
     * 播放类型	0：离线播放；1：在线播放
     */
    private String type = ReportConstants.PLAY_TYPE_ON_LINE;
    /**
     * 播放器位置	1：app播放器；2：外部播放器
     */
    private String position = ReportConstants.POSITION_INNER_APP;

    /**
     * 播放器实际播放时长
     */
    private String playtime;

    /**
     * 播放比率
     */
    private String playrate;
    /**
     * 单曲总时长
     */
    private String length;


    /**
     * 开始播放的时间戳，精确到毫秒
     */
    private String remarks2;
    /**
     * 首次收听 0: 否; 1:是
     */
    private String remarks4 = "0";

    /**
     * 内置播放器必传，固定值：player
     */
    private String remarks6;

    /**
     * 1：播放器自动切换；2 其他; 3：手动（点击）；4：语音；5：方控
     */
    private String remarks7 = ReportConstants.PLAY_CHANGE_BY_CLICK;
    /**
     * 播放内容获取方式
     */
    private String remarks8;
    /**
     * 搜索结果追踪号
     */
    private String remarks9;

    /**
     * 开机收听 0:否；1：是
     */
    private String remarks10;

    private String ai_mz_location;

    /**
     * 播放来源
     */
    private String source;

    /**
     * 推荐结果追踪号
     */
    private String remarks11;

    /**
     * 1语音点播；2搜索结果选择；3其他 4断点续播 5一键收听
     */
    private String remarks3;

    /**
     * 精品 VIP 无
     */
    private String tag;
    /**
     * 0免费 1付费 2试听
     */
    private int audioid_type;

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public int getAudioid_type() {
        return audioid_type;
    }

    public void setAudioid_type(int audioid_type) {
        this.audioid_type = audioid_type;
    }

    public EndListenReportEvent() {
        setEventcode(ReportConstants.EVENT_ID_LISTEN_END);
    }

    public void playParameterToEvent(PlayReportParameter parameter) {
        setAlbumid(parameter.getAlbumid());
        setAudioid(parameter.getAudioid());
        long total = parameter.getTotalLength() / 1000;
        long play = parameter.getPlayPosition() / 1000;
        setLength(String.valueOf(total));
        if (play > total) {
            play = total;
        }
        setPlaytime(String.valueOf(play));
        setRadioid(parameter.getRadioid());
        setPosition(parameter.getPosition());
        try {
            float rate = (float) play / (float) total;
            setPlayrate(String.format("%.2f", rate));
        } catch (Exception e) {

        }

        setRemarks2(parameter.getStartTime());
        setRemarks3(parameter.getContentObtainType());
        setRemarks4(parameter.getIsFirst());
        setRemarks10(parameter.getIsStartFirstPlay());
        if (!StringUtil.isEmpty(parameter.getChangeType())) {
            setRemarks7(parameter.getChangeType());
        }
        setRemarks8(parameter.getContentObtainType());
        if (!StringUtil.isEmpty(parameter.getSearchResultContent())) {
            setRemarks9(parameter.getSearchResultContent());
        }

        setPlayid(parameter.getPlayId());
        if (!StringUtil.isEmpty(parameter.getInnerPlayer())) {
            Logging.i(ReportConstants.REPORT_TAG, "是内部播放器");
            setRemarks6(parameter.getInnerPlayer());
        }
        setAi_mz_location(parameter.getRadioType());
        setSource(parameter.getAudioSource());
        if (!StringUtil.isEmpty(parameter.getRecommendResultCallback())) {
            setRemarks11(parameter.getRecommendResultCallback());
        }
        setAudioid_type(parameter.getAudioid_type());
        setTag(parameter.getTag());
    }

    public String getAudioid() {
        return audioid;
    }

    public void setAudioid(String audioid) {
        this.audioid = audioid;
    }

    public String getRadioid() {
        return radioid;
    }

    public void setRadioid(String radioid) {
        this.radioid = radioid;
    }

    public String getAlbumid() {
        return albumid;
    }

    public void setAlbumid(String albumid) {
        this.albumid = albumid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPlaytime() {
        return playtime;
    }

    public void setPlaytime(String playtime) {
        this.playtime = playtime;
    }

    public String getPlayrate() {
        return playrate;
    }

    public void setPlayrate(String playrate) {
        this.playrate = playrate;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getRemarks2() {
        return remarks2;
    }

    public void setRemarks2(String remarks2) {
        this.remarks2 = remarks2;
    }


    public String getRemarks4() {
        return remarks4;
    }

    public void setRemarks4(String remarks4) {
        this.remarks4 = remarks4;
    }


    public String getRemarks6() {
        return remarks6;
    }

    public void setRemarks6(String remarks6) {
        this.remarks6 = remarks6;
    }

    public String getRemarks7() {
        return remarks7;
    }

    public void setRemarks7(String remarks7) {
        this.remarks7 = remarks7;
    }

    public String getRemarks8() {
        return remarks8;
    }

    public void setRemarks8(String remarks8) {
        this.remarks8 = remarks8;
    }

    public String getRemarks9() {
        return remarks9;
    }

    public void setRemarks9(String remarks9) {
        this.remarks9 = remarks9;
    }

    public String getAi_mz_location() {
        return ai_mz_location;
    }

    public void setAi_mz_location(String ai_mz_location) {
        this.ai_mz_location = ai_mz_location;
    }

    public String getRemarks10() {
        return remarks10;
    }

    public void setRemarks10(String remarks10) {
        this.remarks10 = remarks10;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemarks11() {
        return remarks11;
    }

    public void setRemarks11(String remarks11) {
        this.remarks11 = remarks11;
    }

    public String getRemarks3() {
        return remarks3;
    }

    public void setRemarks3(String remarks3) {
        this.remarks3 = remarks3;
    }
}
