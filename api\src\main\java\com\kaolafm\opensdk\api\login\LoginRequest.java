package com.kaolafm.opensdk.api.login;

import android.text.TextUtils;
import android.util.Log;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.KaolaAccessToken;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.login.internal.AuthorInfo;
import com.kaolafm.opensdk.api.login.internal.LinkAccountNumberDTO;
import com.kaolafm.opensdk.api.login.internal.Sex;
import com.kaolafm.opensdk.api.login.model.QRCodeInfo;
import com.kaolafm.opensdk.api.login.model.Success;
import com.kaolafm.opensdk.api.login.model.UserInfo;
import com.kaolafm.opensdk.api.login.model.UserWealth;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.http.error.ErrorCode;
import com.kaolafm.report.ReportHelper;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 登录注销注册网络请求类
 *
 * <AUTHOR> Yan
 * @date 2018/8/2
 */

public class LoginRequest extends BaseRequest {

    private final LoginService mLoginService;

    private static final String AUTHOR_SUCCESS_CODE = "20000";

    private AccessTokenManager mAccessTokenManager;

    public LoginRequest() {
        mLoginService = obtainRetrofitService(LoginService.class);
        mAccessTokenManager = AccessTokenManager.getInstance();
    }


    /**
     * 获取听伴二维码
     */
    public void getQRCode(HttpCallback<QRCodeInfo> callback) {
        getQRCode("", callback);
    }

    /**
     * 获取听伴二维码
     *
     * @param redirectUrl 选填 授权回调地址
     * @param callback    选填 回调
     */
    public void getQRCode(String redirectUrl, HttpCallback<QRCodeInfo> callback) {
        HashMap<String, String> params = new HashMap<>(4);
        params.put("redirect_uri", redirectUrl);
        params.put("response_type", "code");
        params.put("scope", "basis");
        params.put("state", "0");
        doHttpDeal(mLoginService.getQRCode(params), BaseResult::getResult, callback);
    }

    /**
     * 检查听伴二维码状态
     *
     * @param uuid 必填 用户唯一标识，获取二维码时会同时给到该标识。
     */
    public void checkQRCodeStatus(String uuid, HttpCallback<Integer> callback) {
        doHttpDeal(mLoginService.checkQRStatus(uuid), baseResult -> {
                    QRCodeInfo qrCodeInfo = baseResult.getResult();
                    return qrCodeInfo != null ? qrCodeInfo.getStatus() : QRCodeInfo.STATUS_LOSE_EFFICACY;
                },
                callback);
    }

    /**
     * 回调的时候返回用户信息，因为增加了一个登录中的状态，此时会返回昵称和头像
     * @param uuid
     * @param callback
     */
    public void checkQRCodeStatusAndInfo(String uuid, HttpCallback<QRCodeInfo> callback) {
        doHttpDeal(mLoginService.checkQRStatus(uuid), baseResult -> {
                    return baseResult.getResult();
                },
                callback);
    }

    /**
     * 根据uuid获取用于绑定K-radio的code。需要确定二维码状态已授权 @see{@link QRCodeInfo#STATUS_AUTHORIZATION}
     *
     * @param uuid 必填 用户唯一标识，获取二维码时会同时给到该标识。
     */
    public void fetchCode(String uuid, HttpCallback<String> callback) {
        doHttpDeal(mLoginService.fetchCode(uuid), baseResult -> {
            QRCodeInfo qrCodeInfo = baseResult.getResult();
            if (qrCodeInfo != null) {
                String code = qrCodeInfo.getCode();
                return code == null ? "" : code;
            }
            return "";
        }, callback);
    }

    /**
     * 授权绑定设备
     *
     * @param code     必填 用于绑定设备的code值，通过{@link #fetchCode(String, HttpCallback)}获得
     * @param callback 回调 返回true表示授权绑定成功，false失败
     */
    public void authorized(String code, HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.authorized(mProfileLazy.get().getAppKey(), code), this::saveToken, callback);
    }

    /**
     * 获取用户信息。需要授权成功。
     *
     * @param callback 回调 返回用户信息{@link UserInfo}
     */
    public void getUserInfo(HttpCallback<UserInfo> callback) {
        doHttpDeal(mLoginService.getUserInfo(), BaseResult::getResult, callback);
    }


    /**
     * 根据code进行授权
     *
     * @param code     必填 用于绑定设备的code值，通过{@link #fetchCode(String, HttpCallback)}获得
     * @param callback 回调 返回用户信息{@link UserInfo}
     */
    public void authorizedByCode(String code, HttpCallback<UserInfo> callback) {
        Observable<BaseResult<UserInfo>> observable = mLoginService
                .authorized(mProfileLazy.get().getAppKey(), code)
                .concatMap(baseResult -> {
                    boolean success = saveToken(baseResult);
                    return success ? mLoginService.getUserInfo() : null;
                });
        doHttpDeal(observable, this::saveUserId, callback);
    }

    /**
     * 根据uuid进行授权。需要确定二维码状态已授权 @see{@link QRCodeInfo#STATUS_AUTHORIZATION}
     *
     * @param uuid     必填 用户唯一标识，获取二维码时会同时给到该标识。
     * @param callback 回调 返回用户信息{@link UserInfo}
     */
    public void authorizedByUuid(String uuid, HttpCallback<UserInfo> callback) {
        Observable<BaseResult<UserInfo>> observable = mLoginService.fetchCode(uuid)
                .concatMap(baseResult -> {
                    QRCodeInfo qrCodeInfo = baseResult.getResult();
                    return qrCodeInfo != null ? mLoginService
                            .authorized(mProfileLazy.get().getAppKey(), qrCodeInfo.getCode()) : null;
                })
                .concatMap(baseResult -> {
                    boolean success = saveToken(baseResult);
                    return success ? mLoginService.getUserInfo() : null;
                });
        doHttpDeal(observable, this::saveUserId, callback);
    }

    /**
     * 当授权成功时，自动进行绑定登录。如果要在二维码过期时会回调onError()。
     * <p>
     * 注意：使用该接口要在适当的时候(如页面销毁)进行取消操作{@link #cancel(Object)}
     * </p>
     *
     * @param uuid     必填 用户唯一标识，获取二维码时会同时给到该标识。
     * @param time     必填 轮询间隔，单位毫秒
     * @param callback 回调
     */
    public void loginWhenAuthorized(String uuid, long time, HttpCallback<UserInfo> callback) {
        Observable<BaseResult<UserInfo>> observable = mLoginService.checkQRStatus(uuid)
                .takeWhile(baseResult -> {
                    QRCodeInfo qrCodeInfo = baseResult.getResult();
                    return qrCodeInfo != null &&
                            qrCodeInfo.getStatus() != QRCodeInfo.STATUS_NORMAL &&
                            qrCodeInfo.getStatus() != QRCodeInfo.STATUS_SCANED;
                })
                .repeatWhen(objectObservable -> objectObservable.flatMap(
                        o -> {
                            int status = (int) o;
                            //如果是0就是继续轮询
                            if (status == QRCodeInfo.STATUS_NORMAL || status == QRCodeInfo.STATUS_SCANED) {
                                return Observable.just(1).delay(time, TimeUnit.MILLISECONDS);
                            } else {
                                throw new ApiException(QRCodeInfo.STATUS_LOSE_EFFICACY, "二维码已过期");
                            }
                        }))
                .concatMap(baseResult -> {
                    if (baseResult.getResult().getStatus() == QRCodeInfo.STATUS_AUTHORIZATION) {
                        return mLoginService.fetchCode(uuid);
                    } else {
                        throw new ApiException(QRCodeInfo.STATUS_LOSE_EFFICACY, "二维码已过期");
                    }
                })
                .concatMap(baseResult -> {
                    QRCodeInfo qrCodeInfo = baseResult.getResult();
                    return qrCodeInfo != null ? mLoginService
                            .authorized(mProfileLazy.get().getAppKey(), qrCodeInfo.getCode()) : null;
                })
                .concatMap(baseResult -> {
                    boolean success = saveToken(baseResult);
                    return success ? mLoginService.getUserInfo() : null;
                });
        doHttpDeal(observable, BaseResult::getResult, callback);
    }

    /**
     * 当授权成功时，自动进行绑定登录。如果要在二维码过期时会回调onError()。轮询时间为1000毫秒
     * <p>
     * 注意：使用该接口要在适当的时候(如页面销毁)进行取消操作{@link #cancel(Object)}
     * </p>
     *
     * @param uuid     必填 用户唯一标识，获取二维码时会同时给到该标识。
     * @param callback 回调
     */
    public void loginWhenAuthorized(String uuid, HttpCallback<UserInfo> callback) {
        loginWhenAuthorized(uuid, 1000, callback);
    }

    /**
     * 刷新token接口
     *
     * @param callback true表示刷新成功，false表示刷新失败
     */
    public void refreshToken(HttpCallback<Boolean> callback) {
        KaolaAccessToken kaolaAccessToken = mAccessTokenManager.getKaolaAccessToken();
        Single<BaseResult<KaolaAccessToken>> observable = mLoginService
                .refreshToken(mProfileLazy.get().getAppKey(), kaolaAccessToken.getRefreshToken());
        doHttpDeal(observable, this::saveToken, callback);
    }

    public Single<BaseResult<KaolaAccessToken>> refreshToken() {
        KaolaAccessToken kaolaAccessToken = mAccessTokenManager.getKaolaAccessToken();
        Single<BaseResult<KaolaAccessToken>> observable = mLoginService
                .refreshToken(mProfileLazy.get().getAppKey(), kaolaAccessToken.getRefreshToken())
                .subscribeOn(Schedulers.io())
                .unsubscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .doOnSuccess(baseResult -> {
                    if (baseResult.getCode() == ErrorCode.REFRESH_TOKEN_EXPIRED) {
                        AccessTokenManager.getInstance().logoutKaola();
                    }else {
                        saveToken(baseResult);
                    }
                });
        return observable;
    }

    /**
     * 退出云听。
     *
     * @param callback 回调 返回true表示退出成功，否则失败。
     */
    public void logoutYunting(HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.revokeAuthorization(mProfileLazy.get().getAppKey()), baseResult -> {
            Success success = baseResult.getResult();
            boolean isSuccess = success != null && Integer.parseInt(success.getCode()) == Success.STATUS_SUCCESS;
            if (isSuccess) {
                mAccessTokenManager.logoutKaola();
            }
            return isSuccess;
        }, callback);
    }

    /**
     * 退出。之前使用的方法名为logoutTingban，保留一下
     *
     * @param callback 回调 返回true表示退出成功，否则失败。
     */
    public void logoutTingban(HttpCallback<Boolean> callback) {
        logoutYunting(callback);
    }

    /**
     * 根据第三方账号的uid和token打通账号，就是校验第三方用户的合法性。
     *
     * @param userId          必填 第三方账号的uid，用户的唯一标识，可以和token一起在对方服务器校验用户合法性
     * @param userToken       必填 第三方账号的token，可以和userId一起在对方服务器校验用户合法性
     * @param tokenActiveTime 选填 token的有效时间
     */
    public void linkAccount(String userId, String userToken, long tokenActiveTime, HttpCallback<Boolean> callback) {
        LinkAccountNumberDTO params = new LinkAccountNumberDTO();
        params.setUserId(userId);
        params.setUserToken(userToken);
        params.setAppId(mProfileLazy.get().getAppId());
        params.setTokenActiveTime(tokenActiveTime);
        String body = mGsonLazy.get().toJson(params);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mLoginService.linkAccount(requestBody),
                baseResult -> {
                    Success success = baseResult.getResult();
                    if (success == null || !TextUtils.equals(success.getCode(), Success.VERIFICATION_SUCCESS)) {
                        throw new ApiException(40100, "身份验证未通过");
                    }
                    return true;
                }, callback);
    }

    /**
     * 保存考拉accessToken
     *
     * @param baseResult 授权或刷新token返回的BaseResult
     * @return true表示获取/刷新token成功，false表示失败
     */
    private boolean saveToken(BaseResult<KaolaAccessToken> baseResult) {
        KaolaAccessToken accessToken = baseResult.getResult();
        if (accessToken != null) {
            KaolaAccessToken kaolaAccessToken = mAccessTokenManager.getKaolaAccessToken();
            accessToken.setOpenId(kaolaAccessToken.getOpenId());
            accessToken.setExpireTime(
                    DateUtil.getServerTime() + (kaolaAccessToken.getRefreshTime() - 10 * 1000));
            mAccessTokenManager.setCurrentAccessToken(accessToken);
            ReportHelper.getInstance().initUid(kaolaAccessToken.getUserId());
            return true;
        }
        return false;
    }

    private UserInfo saveUserId(BaseResult<UserInfo> baseResult) {
        UserInfo userInfo = baseResult.getResult();
        if (userInfo != null) {
            KaolaAccessToken kaolaAccessToken = mAccessTokenManager.getKaolaAccessToken();
//            String uid = userInfo.getUid();
//            kaolaAccessToken.setUserId(uid);
//            mAccessTokenManagerLazy.get().setCurrentAccessToken(kaolaAccessToken);
//            ReportHelper.getInstance().initUid(uid);
        }
        return userInfo;
    }

    public void getUserWealth(HttpCallback<UserWealth> callback) {
        doHttpDeal(mLoginService.getUserWealth(), BaseResult::getResult, callback);
    }

    //************************************以下方法已废弃*******************************************//

    @Deprecated
    public void getKaolaQRCode(HttpCallback<QRCodeInfo> callback) {
        getQRCode(callback);
    }

    /**
     * 检测手机号是否已经注册
     *
     * @param phoneNum 必填 手机号
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void checkPhoneIsRegistered(String phoneNum, HttpCallback<Success> callback) {
        doHttpDeal(mLoginService.checkPhoneIsRegistered(phoneNum), BaseResult::getResult, callback);
    }

    /**
     * 获取手机验证码
     *
     * @param phoneNum 必填 手机号
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void getVerificationCode(String phoneNum, HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.getVerificationCode(phoneNum),
                baseResult -> {
                    Success result = baseResult.getResult();
                    return result != null && Success.CODE_SUCCESS.equals(result.getCode());
                }, callback);
    }

    /**
     * 注册
     *
     * @param phoneNum 必填 手机号
     * @param code     必填 验证码
     * @param sex      必填 性别 0 男，1 女
     * @param age      必填 年龄
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void register(String phoneNum, String code, @Sex int sex, String age, HttpCallback<Boolean> callback) {
        HashMap<String, String> params = new HashMap<>();
        params.put(LoginService.PARAMETER_PHONE_NUMBER, phoneNum);
        params.put(LoginService.PARAMETER_VALIDATE_CODE, code);
        params.put("sex", String.valueOf(sex));
        params.put("age", age);
        doHttpDeal(mLoginService.register(params),
                baseResult -> saveAccessToken(baseResult.getResult()), callback);
    }

    /**
     * 登录
     *
     * @param phoneNum 必填 手机号
     * @param code     必填 验证码
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void login(String phoneNum, String code, HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.login(phoneNum, code),
                baseResult -> saveAccessToken(baseResult.getResult()), callback);
    }

    /**
     * 退出登录
     *
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void logout(HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.logout(), successBaseResult -> {
            if (Success.CODE_SUCCESS.equals(successBaseResult.getResult().getCode())) {
                mAccessTokenManager.logoutKaola();
                return true;
            }
            return false;
        }, callback);
    }

    /**
     * 绑定kradio
     *
     * @param code 必填 用于绑定kradio的code值，通过{@link #fetchCode(String, HttpCallback)}获得
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void bindKradio(String code, HttpCallback<UserInfo> callback) {
        doHttpDeal(mLoginService.bindKradio(code), BaseResult::getResult, callback);
    }

    /**
     * 根据UUID绑定K-radio
     *
     * @param uuid 必填 用户唯一标识，获取二维码时会同时给到该标识。
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void bindKradioByUuid(String uuid, HttpCallback<UserInfo> callback) {
        fetchCode(uuid, new HttpCallback<String>() {
            @Override
            public void onSuccess(String s) {
                if (!TextUtils.isEmpty(s)) {
                    bindKradio(s, callback);
                } else {
                    if (callback != null) {
                        callback.onError(new ApiException("绑定K-radio失败"));
                    }
                }
            }

            @Override
            public void onError(ApiException exception) {
                if (callback != null) {
                    callback.onError(exception);
                }
            }
        });
    }

    /**
     * 解绑kradio
     *
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void unbindKradio(HttpCallback<Boolean> callback) {
        doHttpDeal(mLoginService.unbindKradio(), baseResult -> {
            UserInfo userInfo = baseResult.getResult();
            boolean success = userInfo != null /*&& userInfo.getStatus() == UserInfo.STATUS_IS_BIND*/;
            if (success) {
                return true;
            } else {
                throw new ApiException("解绑失败");
            }
        }, callback);
    }

    /**
     * 检查是否绑定K-radio
     *
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    private void isBindKradio(HttpCallback<UserInfo> callback) {
        doHttpDeal(mLoginService.isBindKradio(), BaseResult::getResult, callback);
    }

    private Boolean saveAccessToken(AuthorInfo authorInfo) {
        if (authorInfo != null) {
            if (AUTHOR_SUCCESS_CODE.equals(authorInfo.getCode())) {
                KaolaAccessToken kaolaAccessToken = mAccessTokenManager.getKaolaAccessToken();
                kaolaAccessToken.setAccessToken(authorInfo.getToken());
                kaolaAccessToken.setUserId(authorInfo.getUserId());
                kaolaAccessToken.setRefreshToken(authorInfo.getWechatDeviceId());
                ReportHelper.getInstance().initUid(authorInfo.getUserId());
                mAccessTokenManager.setCurrentAccessToken(kaolaAccessToken);
                return true;
            }
        }
        return false;
    }

}
