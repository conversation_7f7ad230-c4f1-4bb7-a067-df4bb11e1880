package com.kaolafm.opensdk.api.broadcast;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.gson.annotations.SerializedName;

public class BroadcastDetails implements Parcelable {

    /**
     * broadcastId : 1600000000198
     * name : 上海第一财经广播
     * img : http://img.kaolafm.net/mz/images/201612/a03deda5-2a06-4a43-a81c-23c8ecedce64/default.jpg
     * classifyName : 省市台
     * isSubscribe : 0
     * playUrl : http://play.x.l.kaolafm.net/live/1600000000198/index.m3u8
     * onLineNum : 0
     * likedNum : 354
     * status : 1
     * classifyId : 2
     * roomId : 48109
     * freq : FM97.7
     * icon : http://img.kaolafm.net/icon/qingting.fm.png
     */

    /** 广播id*/
    @SerializedName("broadcastId")
    private long broadcastId;

    /** 广播名称*/
    @SerializedName("name")
    private String name;

    /** 广播封面URL*/
    @SerializedName("img")
    private String img;

    /** 广播类型名称*/
    @SerializedName("classifyName")
    private String classifyName;

    /** 是否订阅,1=是，0=否*/
    @SerializedName("isSubscribe")
    private int isSubscribe;

    /** 直播流地址*/
    @SerializedName("playUrl")
    private String playUrl;

    /** 在线收听数*/
    @SerializedName("onLineNum")
    private int onLineNum;

    /** 赞数*/
    @SerializedName("likedNum")
    private int likedNum;

    /** 上下线状态,1=上线0=下线*/
    @SerializedName("status")
    private int status;

    /** 广播类型id*/
    @SerializedName("classifyId")
    private int classifyId;

    /** 直播间id*/
    @SerializedName("roomId")
    private int roomId;

    /** 广播频道*/
    @SerializedName("freq")
    private String freq;

    /** 广播图标*/
    @SerializedName("icon")
    private String icon;

    public long getBroadcastId() {
        return broadcastId;
    }

    public void setBroadcastId(long broadcastId) {
        this.broadcastId = broadcastId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getClassifyName() {
        return classifyName;
    }

    public void setClassifyName(String classifyName) {
        this.classifyName = classifyName;
    }

    public int getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(int isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }

    public int getOnLineNum() {
        return onLineNum;
    }

    public void setOnLineNum(int onLineNum) {
        this.onLineNum = onLineNum;
    }

    public int getLikedNum() {
        return likedNum;
    }

    public void setLikedNum(int likedNum) {
        this.likedNum = likedNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getClassifyId() {
        return classifyId;
    }

    public void setClassifyId(int classifyId) {
        this.classifyId = classifyId;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(this.broadcastId);
        dest.writeString(this.name);
        dest.writeString(this.img);
        dest.writeString(this.classifyName);
        dest.writeInt(this.isSubscribe);
        dest.writeString(this.playUrl);
        dest.writeInt(this.onLineNum);
        dest.writeInt(this.likedNum);
        dest.writeInt(this.status);
        dest.writeInt(this.classifyId);
        dest.writeInt(this.roomId);
        dest.writeString(this.freq);
        dest.writeString(this.icon);
    }

    public BroadcastDetails() {
    }

    protected BroadcastDetails(Parcel in) {
        this.broadcastId = in.readLong();
        this.name = in.readString();
        this.img = in.readString();
        this.classifyName = in.readString();
        this.isSubscribe = in.readInt();
        this.playUrl = in.readString();
        this.onLineNum = in.readInt();
        this.likedNum = in.readInt();
        this.status = in.readInt();
        this.classifyId = in.readInt();
        this.roomId = in.readInt();
        this.freq = in.readString();
        this.icon = in.readString();
    }

    public static final Creator<BroadcastDetails> CREATOR = new Creator<BroadcastDetails>() {
        @Override
        public BroadcastDetails createFromParcel(Parcel source) {
            return new BroadcastDetails(source);
        }

        @Override
        public BroadcastDetails[] newArray(int size) {
            return new BroadcastDetails[size];
        }
    };

    @Override
    public String toString() {
        return "BroadcastDetails{" +
                "broadcastId=" + broadcastId +
                ", name='" + name + '\'' +
                ", img='" + img + '\'' +
                ", classifyName='" + classifyName + '\'' +
                ", isSubscribe=" + isSubscribe +
                ", playUrl='" + playUrl + '\'' +
                ", onLineNum=" + onLineNum +
                ", likedNum=" + likedNum +
                ", status=" + status +
                ", classifyId=" + classifyId +
                ", roomId=" + roomId +
                ", freq='" + freq + '\'' +
                ", icon='" + icon + '\'' +
                '}';
    }
}