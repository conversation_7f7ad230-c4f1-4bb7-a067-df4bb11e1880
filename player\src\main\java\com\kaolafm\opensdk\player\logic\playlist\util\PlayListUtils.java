package com.kaolafm.opensdk.player.logic.playlist.util;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.api.live.model.LiveInfoDetail;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.player.logic.model.PlaylistInfo;
import com.kaolafm.opensdk.player.logic.model.item.AlbumPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.OneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.PurchaseOneKeyPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.RadioInfoData;
import com.kaolafm.opensdk.player.logic.playlist.RadioPlayListControl;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/12.
 */

public class PlayListUtils {

    /**
     * AudioDetails列表转化为AlbumPlayItem列表
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToAlbumPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            AlbumPlayItem playItem = translateAlbumToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getAlbumInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getAlbumInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getAlbumInfoData().setListenNum(playlistInfo.getListenNum());
                playItem.getAlbumInfoData().setBreakPointContinue(playlistInfo.getBreakPointContinue());
                if (StringUtil.isEmpty(playItem.getInfoData().getSourceLogo())) {
                    playItem.getInfoData().setSourceLogo(playlistInfo.getSourceLogo());
                    playItem.getInfoData().setSourceName(playlistInfo.getSourceName());
                }
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails列表转化为RadioPlayItem列表
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToRadioPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            RadioPlayItem playItem = (RadioPlayItem) translateRadioToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }

            if (playlistInfo != null) {
                RadioInfoData radioInfoData = playItem.getRadioInfoData();

                radioInfoData.setCountNum(playlistInfo.getCountNum());
                radioInfoData.setFollowedNum(playlistInfo.getFollowedNum());
                radioInfoData.setListenNum(playlistInfo.getListenNum());
                radioInfoData.setRadioId(Long.parseLong(playlistInfo.getId()));
                radioInfoData.setRadioName(playlistInfo.getAlbumName());
                radioInfoData.setRadioPic(playlistInfo.getAlbumPic());


                radioInfoData.setRadioType(playlistInfo.getRadioType());
                if (playlistInfo.getAdZoneChooseType() == RadioPlayListControl.TYPE_ZONE_CHOOSE_DETAILS) {
                    radioInfoData.setAdZoneId(playlistInfo.getAdZoneId());
                } else {
                    radioInfoData.setAdZoneId((int) audioDetails.getAdZoneId());
                }
                radioInfoData.setAdZoneChooseType(playlistInfo.getAdZoneChooseType());
            }

            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails转化为OneKeyPlayItem列表
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToOneKeyPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            OneKeyPlayItem playItem = (OneKeyPlayItem) translateOneKeyToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getRadioInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getRadioInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getRadioInfoData().setListenNum(playlistInfo.getListenNum());
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails列表转化为PurchaseOneKeyPlayItem列表
     * @param audioDetailsList
     * @param playlistInfo
     * @return
     */
    public static ArrayList<PlayItem> audioDetailToPurchaseOneKeyPlayItem(List<AudioDetails> audioDetailsList, PlaylistInfo playlistInfo) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < audioDetailsList.size(); i++) {
            AudioDetails audioDetails = audioDetailsList.get(i);
            if (audioDetails == null) {
                continue;
            }
            PurchaseOneKeyPlayItem playItem = (PurchaseOneKeyPlayItem) translatePurchaseOneKeyToPlayItem(audioDetails);
            if (playItem == null) {
                continue;
            }
            if (playlistInfo != null) {
                playItem.getRadioInfoData().setCountNum(playlistInfo.getCountNum());
                playItem.getRadioInfoData().setFollowedNum(playlistInfo.getFollowedNum());
                playItem.getRadioInfoData().setListenNum(playlistInfo.getListenNum());
            }
            playItems.add(playItem);
        }
        return playItems;
    }

    /**
     * AudioDetails转化为AlbumPlayItem
     * @param audioDetails
     * @return
     */
    public static AlbumPlayItem translateAlbumToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        AlbumPlayItem playItem = new AlbumPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getOfflineInfoData().setOfflineUrl(aacPlayUrl);
        playItem.getPlayUrlData().setAacPlayUrl32(audioDetails.getAacPlayUrl32());
        playItem.getPlayUrlData().setAacPlayUrl64(audioDetails.getAacPlayUrl64());
        playItem.getPlayUrlData().setAacPlayUrl128(audioDetails.getAacPlayUrl128());
        playItem.getPlayUrlData().setAacPlayUrl320(audioDetails.getAacPlayUrl320());
        playItem.getPlayUrlData().setDefaultPlayUrl(aacPlayUrl);
        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getPlayUrlData().setMp3PlayUrl(audioDetails.getMp3PlayUrl32());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * AudioDetails转化为RadioPlayItem
     * @param audioDetails
     * @return
     */
    public static PlayItem translateRadioToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        RadioPlayItem playItem = new RadioPlayItem();
        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.getPlayUrlData().setAacPlayUrl32(audioDetails.getAacPlayUrl32());
        playItem.getPlayUrlData().setAacPlayUrl64(audioDetails.getAacPlayUrl64());
        playItem.getPlayUrlData().setAacPlayUrl128(audioDetails.getAacPlayUrl128());
        playItem.getPlayUrlData().setAacPlayUrl320(audioDetails.getAacPlayUrl320());
        playItem.getPlayUrlData().setDefaultPlayUrl(aacPlayUrl);

        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getPlayUrlData().setMp3PlayUrl(audioDetails.getMp3PlayUrl32());
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        playItem.getRadioInfoData().setMainTitleName(audioDetails.getMainTitleName());
        playItem.getRadioInfoData().setRadioSubTag(audioDetails.getContentTypeName());
        playItem.getRadioInfoData().setRadioSubTagType(audioDetails.getContentType());
        playItem.getRadioInfoData().setSubheadName(audioDetails.getSubheadName());
        playItem.getRadioInfoData().setCallBack(audioDetails.getCallBack());
        playItem.getRadioInfoData().setSource(audioDetails.getSource());

        return playItem;
    }

    /**
     * AudioDetails转化为OneKeyPlayItem
     * @param audioDetails
     * @return
     */
    public static PlayItem translateOneKeyToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        OneKeyPlayItem playItem = new OneKeyPlayItem();

        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        playItem.getPlayUrlData().setAacPlayUrl32(audioDetails.getAacPlayUrl32());
        playItem.getPlayUrlData().setAacPlayUrl64(audioDetails.getAacPlayUrl64());
        playItem.getPlayUrlData().setAacPlayUrl128(audioDetails.getAacPlayUrl128());
        playItem.getPlayUrlData().setAacPlayUrl320(audioDetails.getAacPlayUrl320());
        playItem.getPlayUrlData().setDefaultPlayUrl(aacPlayUrl);
        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getPlayUrlData().setMp3PlayUrl(audioDetails.getMp3PlayUrl32());
        playItem.getRadioInfoData().setRadioId(audioDetails.getCategoryId());
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * AudioDetails转化为PurchaseOneKeyPlayItem
     * @param audioDetails
     * @return
     */
    public static PlayItem translatePurchaseOneKeyToPlayItem(AudioDetails audioDetails) {
        if (audioDetails == null) {
            return null;
        }
        PurchaseOneKeyPlayItem playItem = new PurchaseOneKeyPlayItem();

        playItem.setAudioId(audioDetails.getAudioId());
        playItem.getInfoData().setTitle(audioDetails.getAudioName());
        String aacPlayUrl = audioDetails.getAacPlayUrl();
        playItem.setDuration(audioDetails.getOriginalDuration());
        playItem.setPlayUrl(aacPlayUrl);

        // 增加购买方式及购买信息
        playItem.setPayMethod(audioDetails.getPayMethod());

        //增加audioDetails中携带的购买类型（buyType）和购买状态信息(buyState)
        playItem.setBuyStatus(audioDetails.getBuyStatus());
        playItem.setBuyType(audioDetails.getBuyType());

        //增加playUrlId供播放时通过id获取播放地址
        playItem.setPlayUrlId(audioDetails.getPlayUrlId());

        playItem.setFine(audioDetails.getAlbumIsFine()); //用于保存到历史条目时展示专辑是否是精品

        playItem.setVip(audioDetails.getAlbumIsVip()); //用于保存到历史条目时展示专辑是否是vip

        playItem.getPlayUrlData().setAacPlayUrl32(audioDetails.getAacPlayUrl32());
        playItem.getPlayUrlData().setAacPlayUrl64(audioDetails.getAacPlayUrl64());
        playItem.getPlayUrlData().setAacPlayUrl128(audioDetails.getAacPlayUrl128());
        playItem.getPlayUrlData().setAacPlayUrl320(audioDetails.getAacPlayUrl320());
        playItem.getPlayUrlData().setDefaultPlayUrl(aacPlayUrl);
        playItem.getInfoData().setAudioDes(audioDetails.getAudioDes());
        playItem.getInfoData().setAlbumId(audioDetails.getAlbumId());
        playItem.getInfoData().setAlbumPic(audioDetails.getAlbumPic());
        playItem.getInfoData().setAlbumName(audioDetails.getAlbumName());
        playItem.getInfoData().setAudioPic(audioDetails.getAudioPic());
        playItem.getInfoData().setOrderNum(audioDetails.getOrderNum());
        playItem.getInfoData().setUpdateTime(String.valueOf(audioDetails.getUpdateTime()));
        playItem.getPlayUrlData().setMp3PlayUrl(audioDetails.getMp3PlayUrl32());
        playItem.getRadioInfoData().setRadioId(audioDetails.getCategoryId());
        playItem.getRadioInfoData().setClockId(audioDetails.getClockId());
        playItem.getRadioInfoData().setCategoryId(audioDetails.getCategoryId());
        playItem.getInfoData().setIcon(audioDetails.getIcon());
        playItem.getInfoData().setSourceLogo(audioDetails.getSourceLogo());
        playItem.getInfoData().setSourceName(audioDetails.getSourceName());
        return playItem;
    }

    /**
     * ProgramDetails列表转化为BroadcastPlayItem列表
     * @param programDetailsList
     * @param channel
     * @return
     */
    public static ArrayList<PlayItem> programDetailsToPlayItem(List<ProgramDetails> programDetailsList, String channel) {
        if (ListUtil.isEmpty(programDetailsList)) {
            return null;
        }
        ArrayList<PlayItem> playItems = new ArrayList<>();
        for (int i = 0; i < programDetailsList.size(); i++) {
            ProgramDetails programDetails = programDetailsList.get(i);
            if (programDetails == null) {
                continue;
            }
            playItems.add(translateToPlayItem(programDetails, channel));
        }
        return playItems;
    }


    public static int getLivingBroadcastPlayItem(ArrayList<PlayItem> playItems) {
        if (ListUtil.isEmpty(playItems)) {
            return 0;
        }
        int size = playItems.size();
        for (int i = 0; i < size; i++) {
            PlayItem playItem = playItems.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
            if (broadcastPlayItem.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
                return i;
            }
        }
        return 0;
    }

    /**
     * ProgramDetails转化为BroadcastPlayItem
     * @param programDetails
     * @param channel
     * @return
     */
    public static BroadcastPlayItem translateToPlayItem(ProgramDetails programDetails, String channel) {
        BroadcastPlayItem playItem = new BroadcastPlayItem();

        if (programDetails.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
            playItem.setPlayUrl(programDetails.getPlayUrl());
            playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        } else {
            int status = programDetails.getStatus();
            if (status == PlayerConstants.BROADCAST_STATUS_LIVING ||
                    status == PlayerConstants.BROADCAST_STATUS_DEFAULT) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                playItem.setPlayUrl(programDetails.getPlayUrl());
                playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
            } else if (status == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
                playItem.setPlayUrl(programDetails.getBackLiveUrl());
            } else {
                playItem.setStatus(status);
                playItem.setPlayUrl(null);
            }
        }
        playItem.getInfoData().setDataSrc(PlayerConstants.RESOURCES_TYPE_BROADCAST);
        playItem.getInfoData().setAlbumId(programDetails.getBroadcastId());
        playItem.setAudioId(programDetails.getProgramId());
        playItem.getInfoData().setTitle(programDetails.getTitle());
        playItem.getInfoData().setAlbumPic(programDetails.getBroadcastImg());
        playItem.getInfoData().setAlbumName(programDetails.getBroadcastName());
        playItem.getInfoData().setAudioDes(programDetails.getDesc());
        long start = programDetails.getStartTime();
        long end = programDetails.getFinishTime();
        long duration = end - start;
        playItem.getTimeInfoData().setStartTime(start);
        playItem.getTimeInfoData().setFinishTime(end);
        playItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
        playItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
        playItem.setDuration((int) duration);
        playItem.getInfoData().setIcon(programDetails.getIcon());
        playItem.setFrequencyChannel(channel);
        return playItem;
    }

    /**
     * LiveInfoDetail转化为LivePlayItem
     * @param detail
     * @return
     */
    public static PlayItem liveInfoToPlayItem(LiveInfoDetail detail) {
        LivePlayItem playItem = new LivePlayItem();
        playItem.setPlayUrl(detail.getLiveUrl());
        playItem.setDuration((int) (detail.getFinishTime() - detail.getStartTime()));

        playItem.getInfoData().setTitle(detail.getProgramName());
        playItem.getTimeInfoData().setBeginTime(detail.getBeginTime());
        playItem.getTimeInfoData().setEndTime(detail.getEndTime());
        playItem.getInfoData().setAudioPic(detail.getLivePic());
        playItem.getTimeInfoData().setStartTime(detail.getStartTime());
        playItem.getTimeInfoData().setFinishTime(detail.getFinishTime());
        playItem.getInfoData().setAlbumId(detail.getLiveId());
        playItem.setComperesId(String.valueOf(detail.getComperesId()));
        playItem.setLiveId(detail.getProgramId());
        playItem.setAudioId(detail.getProgramId());
        playItem.setStatus(detail.getStatus());
        return playItem;
    }

}
