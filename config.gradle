ext {
    android = [
            compileSdkVersion            : 28,
            buildToolsVersion            : "28.0.3",
            minSdkVersion                : 17,
            targetSdkVersion             : 28,
            versionCode                  : 10600,
            versionName                  : "1.6.0",
            adVersionCode                : 10200,
            adVersionName                : "1.2.0",
            javaSourceVersion            : JavaVersion.VERSION_1_8,
            javaTargetVersion            : JavaVersion.VERSION_1_8
    ]
    version = [
            androidSupportSdkVersion     : "28.0.0",
            retrofitSdkVersion           : "2.4.0",
            butterknifeSdkVersion        : "9.0.0-SNAPSHOT",
            rxlifecycle2SdkVersion       : "2.2.1",
            canarySdkVersion             : "1.5.4",
            glideVersion                 : "4.6.1",
            dagger2Version               : "2.16"
    ]

    maven = [
            artifactId                   : "open-sdk",
            groupId                      : "com.kaolafm",
            libType                      : "jar",
            libDescription               : "dependences lib",
            LocalRepoUrl                 : readLocalProperties("local.repo.url"),
            UserName                     : "admin",
            Password                     : "QA4A8saRBZFyuicF",

    ]

    config = [
        repository: "pub",
        domain_type: "",
    ]

    dependencies = [
            "junit"                      : "junit:junit:4.12",
            "appcompat-v4"               : "com.android.support:support-v4:${version["androidSupportSdkVersion"]}",
            "appcompat-v7"               : "com.android.support:appcompat-v7:${version["androidSupportSdkVersion"]}",
            "constraint"                 : "com.android.support.constraint:constraint-layout:1.1.1",
            "design"                     : "com.android.support:design:${version["androidSupportSdkVersion"]}",
            "cardview"                   : "com.android.support:cardview-v7:${version["androidSupportSdkVersion"]}",
            "recyclerview-v7"            : "com.android.support:recyclerview-v7:${version["androidSupportSdkVersion"]}",
            "annotations"                : "com.android.support:support-annotations:${version["androidSupportSdkVersion"]}",
            "annotations-java5"          : "org.jetbrains:annotations-java5:15.0",
            "eventbus"                   : "org.greenrobot:eventbus:3.1.1",
            "butterknife"                : "com.jakewharton:butterknife:${version["butterknifeSdkVersion"]}",
            "butterknife-compiler"       : "com.jakewharton:butterknife-compiler:${version["butterknifeSdkVersion"]}",
            "butterknife-plugin"         : "com.jakewharton:butterknife-gradle-plugin:${version["butterknifeSdkVersion"]}",
            "retrofit2"                  : "com.squareup.retrofit2:retrofit:${version["retrofitSdkVersion"]}",
            "retrofit2-gson"             : "com.squareup.retrofit2:converter-gson:${version["retrofitSdkVersion"]}",
            "retrofit2-rxjava2"          : "com.squareup.retrofit2:adapter-rxjava2:${version["retrofitSdkVersion"]}",
            "disklrucache"               : "com.jakewharton:disklrucache:2.0.2",
            "okhttp3"                    : "com.squareup.okhttp3:okhttp:3.10.0",
            "okhttp3-log"                : "com.squareup.okhttp3:logging-interceptor:3.3.1",
            "rxandroid2"                 : "io.reactivex.rxjava2:rxandroid:2.0.2",
            "rxjava2"                    : "io.reactivex.rxjava2:rxjava:2.1.16",
            "rxlifecycle2"               : "com.trello.rxlifecycle2:rxlifecycle:${version["rxlifecycle2SdkVersion"]}",
            "rxlifecycle2-components"    : "com.trello.rxlifecycle2:rxlifecycle-components:${version["rxlifecycle2SdkVersion"]}",
            "rxcache2"                   : "com.github.VictorAlbertos.RxCache:runtime:1.8.3-2.x",
            "rxcache2-gson"              : "com.github.VictorAlbertos.Jolyglot:gson:0.0.4",
            "canary-debug"               : "com.squareup.leakcanary:leakcanary-android:${version["canarySdkVersion"]}",
            "canary-release"             : "com.squareup.leakcanary:leakcanary-android-no-op:${version["canarySdkVersion"]}",
            "multidex"                   : "com.android.support:multidex:1.0.2",
            "lottie"                     : "com.airbnb.android:lottie:2.5.0",
            "logger"                     : "com.orhanobut:logger:2.2.0",
            "glide"                      : "com.github.bumptech.glide:glide:${version["glideVersion"]}",
            "glide-compiler"             : "com.github.bumptech.glide:compiler:${version["glideVersion"]}",
            "gson"                       : "com.google.code.gson:gson:2.8.2",
            "greenDao"                   : "org.greenrobot:greendao:3.3.0",
            "arouter"                    : "com.alibaba:arouter-api:1.3.1",
            "arouter-compiler"           : "com.alibaba:arouter-compiler:1.1.4",
            "zxing"                      : "com.google.zxing:core:3.3.2",
            "bugly_crashreport"          : "com.tencent.bugly:crashreport:latest.release",
            "bugly_nativecrashreport"    : "com.tencent.bugly:nativecrashreport:latest.release",
            "dagger2"                    : "com.google.dagger:dagger:${version["dagger2Version"]}",
            "dagger2-compiler"           : "com.google.dagger:dagger-compiler:${version["dagger2Version"]}",
            "dagger2-android"            : "com.google.dagger:dagger-android:${version["dagger2Version"]}",
            "dagger2-android-support"    : "com.google.dagger:dagger-android-support:${version["dagger2Version"]}",
            "dagger2-android-processor"  : "com.google.dagger:dagger-android-processor:${version["dagger2Version"]}",
            "openSDK"                    : "com.kaolafm:open-sdk:1.6.0.11",
            "ad"                         : "com.kaolafm:ad:1.2.0.1"

    ]
}
def readLocalProperties(String name) {
    def properties = new Properties()
    File file = project.rootProject.file('local.properties')
    if (file.exists()) {
        def inputStream = file.newDataInputStream()
        properties.load(inputStream)
        if (properties.containsKey(name)) {
            return properties.getProperty(name)
        }
    }
}