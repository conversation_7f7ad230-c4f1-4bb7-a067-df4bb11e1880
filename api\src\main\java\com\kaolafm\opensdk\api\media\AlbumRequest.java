package com.kaolafm.opensdk.api.media;

import android.support.annotation.IntDef;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: AlbumRequest.java                                               
 *                                                                  *
 * Created in 2018/8/10 下午2:50                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class AlbumRequest extends BaseRequest {

    private AlbumService mService;

    public AlbumRequest() {
        mService = obtainRetrofitService(AlbumService.class);
    }


    /**
     * 获取专辑详情
     *
     * @param albumId  专辑Id
     * @param callback 回调
     */
    public void getAlbumDetails(long albumId, HttpCallback<AlbumDetails> callback) {
        doHttpDeal(mService.getAlbumDetails(albumId), baseResult -> {
            List<AlbumDetails> list = baseResult.getResult();
            if (!ListUtil.isEmpty(list)) {
                return list.get(0);
            }
            return null;
        }, callback);
    }

    /**
     * 一次请求多个专辑详情
     *
     * @param albumIds 专辑id数组
     * @param callback 回调
     */
    public void getAlbumDetails(Long[] albumIds, HttpCallback<List<AlbumDetails>> callback) {
        String albumId = StringUtil.array2String(albumIds);
        doHttpDeal(mService.getAlbumDetails(albumId), BaseResult::getResult, callback);
    }


    /**
     * 正序
     */
    public static final int SORT_ACS = 1;

    /**
     * 倒序
     */
    public static final int SORT_DESC = -1;

    @IntDef({SORT_ACS, SORT_DESC})
    public @interface Sort {

    }


    /**
     * 请求播单
     *
     * @param albumId  专辑id
     * @param audioId  单曲ID: 根据单曲id定位分页（此参数可传0）
     * @param sort     -1倒序 1正序
     * @param pageSize 请求页码 1, 2, 3...
     * @param pageNum  每页条目数
     * @param callback 回调
     */
    public void getPlaylist(long albumId, long audioId, @Sort int sort, int pageSize, int pageNum,
            HttpCallback<BasePageResult<List<AudioDetails>>> callback) {
        doHttpDeal(mService.getPlaylist(albumId, sort, pageSize, pageNum, audioId), BaseResult::getResult, callback);
    }


    /**
     * 请求播单
     *
     * @param albumId  专辑id
     * @param sort     -1倒序 1正序
     * @param pageSize 请求页码 1, 2, 3...
     * @param pageNum  每页条目数
     * @param callback 回调
     */
    public void getPlaylist(long albumId, @Sort int sort, int pageSize, int pageNum,
            HttpCallback<BasePageResult<List<AudioDetails>>> callback) {
        getPlaylist(albumId, 0, sort, pageSize, pageNum, callback);
    }

}
