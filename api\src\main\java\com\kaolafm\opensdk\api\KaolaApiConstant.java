package com.kaolafm.opensdk.api;


/**
 * 考拉接口相关常量
 *
 * <AUTHOR> on 2018/5/16.
 */
public final class KaolaApiConstant {

    public static final String SIGN = "sign";

    public static final String OPEN_ID = "openid";

    public static final String APP_ID = "appid";

    public static final String APP_KEY = "appkey";

    public static final String DEVICE_ID = "deviceid";

    public static final String OS = "os";

    public static final String CHANNEL = "channel";

    public static final String PACKAGE_NAME = "packagename";

    public static final String OS_NAME = "android";

    public static final String USER_ID = "uid";

    public static final String VERSION = "version";

    public static final String SDK_VERSION = "sdkversion";

    public static final String LAT = "lat";

    public static final String LNG = "lng";

    public static final String TOKEN = "token";

    public static final String UDID = "udid";

    public static final String OPEN_UID = "open_uid";

    public static final String ACCESS_TOKEN = "access_token";

    /**
     * 时间戳
     */
    public static final String TIMESTAMP = "timestamp";

    /**
     * 随机数
     */
    public static final String NONCE = "nonce";

    /**
     * 车型。
     */
    public static final String CAR_TYPE = "carType";

    /**
     * KRadio userid
     */
    public static final String KRADIO_USER_ID = "kradioUid";

    public static final String OSVERSION = "osversion";

    public static final String DEVICE_NAME = "devicename";

    public static final String RESOLUTION = "resolution";

    public static final String SCREEN_SIZE = "screensize";

    public static final String IMEI = "imei";

    public static final String DEVICE_TYPE = "devicetype";


    private static final String KAOLA_VERSION = "/v2";

    /**
     * 未登录的path，现在有订阅和历史
     */
    public static final String KRADIO_USER = "/kradio/user";

    /**
     * 登录状态下的path，现在有订阅历史
     */
    public static final String AUTH_USER = "/authuser";

    /**
     * 强账号体系下，与账号相关的url路径。有订阅、历史、个性化标签
     */
    private static final String AU_PATH = KAOLA_VERSION + "/au";

    private static final String SWITCH_USER_PATH = KAOLA_VERSION + KRADIO_USER;

    /**
     * 设备激活页面数据
     */
    public static final String REQUEST_K_RADIO_ACTIVATE_PAGE = "/thirdpart/activate/mainpage";

    /**
     * 激活接口 用于获取auto id
     */
    public static final String REQUEST_K_RADIO_ACTIVATION = "/thirdpart/activate/active";

    /**
     * 初始化接口 用于重新获取auto id
     */
    public static final String REQUEST_K_RADIO_ACTIVATION_INIT = "/thirdpart/activate/init";

    /**
     * 刷新token接口
     */
    public static final String REQUEST_K_RADIO_REFRESH_TOKEN = "/thirdpart/activate/refreshtoken";

    /**
     * 用户注册接口
     */
    public static final String REQUEST_K_RADIO_REGISTER = "/thirdpart/register/register";

    /**
     * 获取验证码接口
     */
    public static final String REQUEST_K_RADIO_VALIDATE_CODE = "/thirdpart/register/validatecode";

    /**
     * 验证手机是否注册过
     */
    public static final String REQUEST_K_RADIO_PHONE_IS_REGISTERED = "/thirdpart/register/isregistered";

    /**
     * 用户登录接口
     */
    public static final String REQUEST_K_RADIO_LOGIN = "/thirdpart/user/login";

    /**
     * 退出登录
     */
    public static final String REQUEST_K_RADIO_LOGOUT = "/thirdpart/user/loginout";

    /**
     * 获取品牌信息，包括名称，log，用户须知
     */
    public static final String GET_BRAND_INFO = "/thirdpart/activate/mainpage";

    /**
     * 考拉初始化
     */
    public static final String REQUEST_KAOLA_INIT = KAOLA_VERSION + "/app/init";

    /**
     * 考拉激活
     */
    public static final String REQUEST_KAOLA_ACTIVATE = KAOLA_VERSION + "/app/active";

    public static final String GET_BRAND = KAOLA_VERSION + "/kradio/getCarBrandStr";

    /**
     * 微信互联接口
     */
    public static final String REQUEST_KAOLA_WX_CONNECT = KAOLA_VERSION + "/wx/wechatConnect/sendCacheMessage";

    /*******************************************运营接口**************************************************/

    private static final String OPERATION_PATH = KAOLA_VERSION + "/operation";

    /**
     * 递归获取某一内容类型的整颗分类树
     */
    public static final String GET_CATEGORY_TREE = OPERATION_PATH + "/categoryTree";

    /**
     * 获取某一内容类型的根分类列表
     */
    public static final String GET_CATEGORY_ROOT = OPERATION_PATH + "/categoryRoot";

    /**
     * 获取某一内容类型的根分类列表
     */
    public static final String GET_SUBCATEGORY_LIST = OPERATION_PATH + "/subCategories";

    /**
     * 根据分类编码获取分类成员数量
     */
    public static final String GET_CATEGORY_MEMBER_NUM = OPERATION_PATH + "/categoryMembersNum";

    /**
     * 根据分类编码分页获取分类成员列表
     */
    public static final String GET_CATEGORY_MEMBER_LIST_NEW = OPERATION_PATH + "/categoryMembers";

    /**
     * 递归获取整个栏目树
     */
    public static final String GET_COLUMN_TREE = OPERATION_PATH + "/columnTree";

    /**
     * 根据父栏目编码获取下级栏目列表
     */
    public static final String GET_SUBCOLUMN_LIST = OPERATION_PATH + "/subColumns";

    /**
     * 根据父栏目编码获取下级栏目列表
     */
    public static final String GET_COLUMN_MEMBER_LIST = OPERATION_PATH + "/columnMembers";

    //-----------------------------以下接口已过时--------------------------------------------------
    /**
     * 获取栏目列表
     */
    public static final String GET_COLUMN_LIST = KAOLA_VERSION + "/operation/column/list";

    /**
     * 获取分类列表
     */
    public static final String GET_CATEGORY_LIST = KAOLA_VERSION + "/operation/category/list";

    /**
     * 根据id获取分类信息
     */
    public static final String GET_CATEGORY_INFO = KAOLA_VERSION + "/operation/category/{id}";

    /**
     * 获取分类成员列表
     */
    public static final String GET_CATEGORY_MEMBER_LIST = KAOLA_VERSION + "/operation/category/member/list";

    /************************************************************************************************************************/
    private static final String RESOURCE = "/resource";
    /**
     * 语义搜索
     */
    public static final String SEARCH_BY_SEMANTICS = RESOURCE + "/voice/searchall";

    /**
     * 搜索所有类型的数据
     */
    public static final String SEARCH_ALL = RESOURCE + "/searchall";

    /**
     * 根据资源不同类型搜索
     */
    public static final String SEARCH_BY_TYPE = RESOURCE + "/searchtype";

    /**
     * 获取搜索联想词
     */
    public static final String GET_SUGGESTED_WORDS = RESOURCE + "/autoSuggestionall";

    /**
     * 获取搜索热词
     */
    public static final String GET_HOT_WORDS = RESOURCE + "/autoSearchwords";

    /**
     * 获取专辑详情
     */
    public static final String GET_ALBUM_DETAILS = KAOLA_VERSION + "/album/detail";

    /**
     * 获取Radio(PGC)详情
     */
    public static final String GET_RADIO_DETAILS = KAOLA_VERSION + "/radio/detail";

    /**
     * 获取Radio(PGC)播单
     */
    public static final String GET_RADIO_LIST = KAOLA_VERSION + "/radio/list";

    /**
     * 获取单曲详情(一次获取多个)
     */
    public static final String GET_AUDIO_DETAILS_MULTIPLE = KAOLA_VERSION + "/audio/details";

    /**
     * 获取单曲详情(一次获取一个)
     */
    public static final String GET_AUDIO_DETAILS_SINGLE = KAOLA_VERSION + "/audio/detail";

    /**
     * 获取单曲播放信息（主要用于付费）
     */
    public static final String GET_AUDIO_PLAYINFO_SINGLE = KAOLA_VERSION + "/audio/playInfo";

    /**
     * 获取专辑下期列表接口
     */
    public static final String GET_AUDIO_LIST = KAOLA_VERSION + "/audio/list";

    /**
     * 获取当前时间点的报时声音单曲
     */
    public static final String GET_CURRENT_CLOCK_AUDIO = KAOLA_VERSION + "/time/currentClockAudio";

    /********************************************在线广播***************************************************
     * 获取在线广播列表
     */
    public static final String GET_BROADCAST_LIST = KAOLA_VERSION + "/broadcast/list";

    /**
     * 根据在线广播id返回地方台或国家台在线广播列表
     */
    public static final String GET_BROADCAST_NEIGHBOR_LIST = KAOLA_VERSION + "/broadcast/neighbor/list";

    /**
     * 获取在线广播详情
     */
    public static final String GET_BROADCAST_DETAILS = KAOLA_VERSION + "/broadcast/detail";

    /**
     * 获取在线广播详情
     */
    public static final String GET_BROADCAST_DETAILS_TOMORROW = KAOLA_VERSION + "/broadcast/tomorrow/programlist";

    /********************************************在线广播节目***************************************************
     * 获取在线广播节目列表
     */
    public static final String GET_BROADCAST_PROGRAM_LIST = KAOLA_VERSION + "/broadcast/programlist";

    /**
     * 获取在线广播节目详情
     */
    public static final String GET_BROADCAST_PROGRAM_DETAILS = KAOLA_VERSION + "/program/detail";

    /**
     * 获取在线广播当前节目详情
     */
    public static final String GET_BROADCAST_CURRENT_PROGRAM = KAOLA_VERSION + "/broadcast/currentprogram";


    /*********************************************区域位置**************************************************
     * 获取在线广播区域
     */
    public static final String GET_BROADCAST_AREA_LIST = KAOLA_VERSION + "/broadcast/arealist";

    /**
     * 根据经纬度获取区域
     */
    public static final String GET_BROADCAST_AREA = KAOLA_VERSION + "/broadcast/getarea";

    /**
     * 获取城市在线广播列表,排行榜
     */
    public static final String GET_CITY_BROADCAST_LIST_TOP = KAOLA_VERSION + "/broadcast/getCityBroadcast/topList";

    //****************************************考拉账号相关**************************************************/

    /**
     * 获取考拉登录二维码
     */
    public static final String GET_QR_CODE = KAOLA_VERSION + "/oauth2/qrcode";

    /**
     * 检查二维码状
     */
    public static final String CHECK_QR_STATUS = KAOLA_VERSION + "/oauth2/qrcheck";

    /**
     * 扫码后获取code接口
     */
    public static final String FETCH_CODE = KAOLA_VERSION + "/oauth2/fetchcode";

    /**
     * 获取token并绑定设备
     */
    public static final String GET_TOKEN_AND_BIND = KAOLA_VERSION + "/oauth2/token";

    /**
     * 刷新token
     */
    public static final String REFRESH_TOKEN = KAOLA_VERSION + "/oauth2/refreshtoken";

    /**
     * 获取用户信息
     */
    public static final String GET_USER_INFO = KAOLA_VERSION + "/authuser/userinfo";

    /**
     * 取消授权
     */
    public static final String REVOKE_AUTHORIZATION = KAOLA_VERSION + "/oauth2/revoke";

    /*-----------------------------------K-radio绑定相关-----------
     * 根据code绑定
     */
//    @Deprecated
//    public static final String GET_USER_INFO = KAOLA_VERSION + "/oauth2/kradio";

    /*
     * 退出考拉绑定
     */
//    @Deprecated
//    public static final String KAOLA_LOGOUT = "/thirdpart/access/kradioUnbind";

    /*
     * 绑定kradio
     */
//    @Deprecated
//    public static final String BIND_KRADIO = "/thirdpart/kaola/bind";

    /*
     * 解绑kradio
     */
//    @Deprecated
//    public static final String UNBIND_KRADIO = "/thirdpart/kaola/unbind";

    /*
     * 检查kradio是否绑定
     */
//    @Deprecated
//    public static final String CHECK_IS_BIND_KRADIO = "/thirdpart/kaola/isbind";


    //****************************************订阅相关**************************************************/

    /**
     * ---------------------------------kradio弱账号接口-----------------------------
     * <p>
     * 获取订阅列表
     */
    public static final String KRADIO_GET_SUBSCRIBE_LIST = SWITCH_USER_PATH + "/subscribelist";

    /**
     * 订阅
     */
    public static final String KRADIO_SUBSCRIBE = SWITCH_USER_PATH + "/subscribe";

    /**
     * 取消订阅
     */
    public static final String KRADIO_UNSUBSCRIBE = SWITCH_USER_PATH + "/unsubscribe";

    /**
     * 是否订阅
     */
    public static final String KRADIO_CHECK_IS_SUBSCRIBE = SWITCH_USER_PATH + "/issubscribe";

    /**
     * 获取用户的订阅，返回的是类似PGC的流，用于一键播放。
     */
    public static final String KRADIO_GET_USER_FOLLOW_RADIO = SWITCH_USER_PATH + "/followradio";

    /**
     * ----------------------------------订阅相关考拉open接口--------------------------------
     * 获取订阅列表
     */
    public static final String GET_SUBSCRIBE_LIST = KAOLA_VERSION + "/subscribelist";

    /**
     * 订阅
     */
    public static final String SUBSCRIBE = KAOLA_VERSION + "/authuser/subscribe";

    /**
     * 取消订阅
     */
    public static final String UNSUBSCRIBE = KAOLA_VERSION + "/authuser/unsubscribe";

    /**
     * 是否订阅
     */
    public static final String CHECK_IS_SUBSCRIBE = KAOLA_VERSION + "/authuser/issubscribe";

    /**
     * 获取用户的订阅，返回的是类似PGC的流，用于一键播放。
     */
    public static final String GET_USER_FOLLOW_RADIO = KAOLA_VERSION + "/authuser/followradio";


    /**
     * ---------------------------------kradio强账号接口-----------------------------
     */
    private static final String SUBSCRIPTION_PATH_STRONG = AU_PATH + "/subscription";
    /**
     * <p>
     * 获取订阅列表
     */
    public static final String KRADIO_GET_SUBSCRIBE_LIST_STRONG = SUBSCRIPTION_PATH_STRONG + "/list";

    /**
     * 订阅
     */
    public static final String KRADIO_SUBSCRIBE_STRONG = SUBSCRIPTION_PATH_STRONG + "/subscribe";

    /**
     * 取消订阅
     */
    public static final String KRADIO_UNSUBSCRIBE_STRONG = SUBSCRIPTION_PATH_STRONG + "/cancel";

    /**
     * 是否订阅
     */
    public static final String KRADIO_CHECK_IS_SUBSCRIBE_STRONG = SUBSCRIPTION_PATH_STRONG + "/exists";

    /**
     * 获取用户的订阅，返回的是类似PGC的流，用于一键播放。
     */
    public static final String KRADIO_GET_USER_FOLLOW_RADIO_STRONG = SUBSCRIPTION_PATH_STRONG + "/flow";


    //**********************************直播*******************************************/

    /**
     * 直播info
     */
    public static final String REQUEST_KAOLA_LIVE_INFO = KAOLA_VERSION + "/liveplay/detail?";

    /**
     * 云信帐户
     */
    public static final String REQUEST_KAOLA_CHAT_ROOM_TOKEN = KAOLA_VERSION + "/liveplay/getaccount";

    /**
     * 根据开发者的唯一标识、头像、昵称获取进入直播的token。
     */
    public static final String GET_CHAT_ROOM_TOKEN_BY_ID = KAOLA_VERSION + "/liveplay/token";

    /**
     * 根据第三方账号的uid和token打通账号，就是校验第三方用户的合法性。
     */
    public static final String LINK_ACCOUNT = KAOLA_VERSION + "/api/checkUidAndToken";

    //**************************************************************************************************************/

    /**
     * 场景推送
     */
    public static final String GET_SCENE_INFO = KAOLA_VERSION + "/kradio/recommend/scene";

    //**************************************************收听历史****************************************************/

    /**
     * --------------------------------K-radio弱账号接口---------------------------
     * 上传收听历史
     */
    public static final String KRADIO_SAVE_LISTENING_HISTORY = SWITCH_USER_PATH + "/savehistory";

    /**
     * 登录状态下的收听历史path
     */
    public static final String LIST_HISTORY_AT_LOGIN = "/listhistory";

    /**
     * 未登录时收听历史path
     */
    public static final String HISTORY_LIST = "/historylist";
    /**
     * 获取收听历史
     */
    public static final String KRADIO_GET_HISTORY_LIST = SWITCH_USER_PATH + HISTORY_LIST;

    /**
     * 清空收听历史
     */
    public static final String KRADIO_CLEAR_LISTENING_HISTORY = SWITCH_USER_PATH + "/clearhistory";

    /**
     * ------------------------------考拉open接口-------------------------------------
     * 上传收听历史
     */
    public static final String SAVE_LISTENING_HISTORY = KAOLA_VERSION + "/authuser/savehistory";

    /**
     * 获取收听历史
     */
    public static final String GET_HISTORY_LIST = KAOLA_VERSION + "/authuser/listhistory";

    /**
     * 清空收听历史
     */
    public static final String CLEAR_LISTENING_HISTORY = KAOLA_VERSION + "/authuser/clearhistory";

    /**
     * --------------------------------K-radio强账号接口---------------------------
     */
    private static final String HISTORY_PATH_STRONG = AU_PATH + "/history";
    /**
     * 上传收听历史
     */
    public static final String KRADIO_SAVE_LISTENING_HISTORY_STRONG = "/v2/kradio/saveHistoryAccumulate";
    /**
     * 获取收听历史
     */
    public static final String KRADIO_GET_HISTORY_LIST_STRONG = HISTORY_PATH_STRONG + "/list";

    /**
     * 清空收听历史
     */
    public static final String KRADIO_CLEAR_LISTENING_HISTORY_STRONG = HISTORY_PATH_STRONG + "/clear";

    /**
     * 版本升级时，通过旧版本的deviceId获取一次设备的历史记录，只第一次请求返回数据，多次请求不返回数据
     */
    public static final String KRADIO_GET_HISTORY_ONCE = KAOLA_VERSION + KRADIO_USER + "/history/consume"+UserUrlHandler.SWITCH_IGNORE;


    /**********************************个性化推荐相关接口*********************************/

    public static final String RECOMMEND = KAOLA_VERSION + "/recommend";
    public static final String AUTHUSER = KAOLA_VERSION + "/authuser";

    /**
     * 根据是否有无用户属性判断是否返回兴趣标签列表
     * 未登录情况下
     */
    public static final String GET_INTEREST_TAG_LIST_UNLOGINED = RECOMMEND + "/rectag/list";
    /**
     * 根据是否有无用户属性判断是否返回兴趣标签列表
     * 登录情况下
     */
    public static final String GET_INTEREST_TAG_LIST_LOGINED = AUTHUSER + "/rectaglist";

    /**
     * 保存用户属性并返回兴趣标签列表
     */
    public static final String SAVE_USER_ATTRIBUTE = RECOMMEND + "/user/attribute";

    /**
     * 保存用户兴趣标签
     */
    public static final String SAVE_INTEREST_TAG = RECOMMEND + "/user/tag";

    /**
     * 强账号下 保存用户兴趣标签
     */
    public static final String SAVE_INTEREST_TAG_STRONG = AU_PATH + "/tag/save";

    /**
     * 强账号下获取个人选中的兴趣标签。
     */
    public static final String GET_PERSONAL_INTEREST_TAG_LIST = AU_PATH + "/tag/list";

    /**
     * 保存第三方用户信息
     */
//    public static final String SAVE_THIRD_USER = RECOMMEND + "/thirduser";
    public static final String SAVE_THIRD_USER = AUTHUSER + "/thirduser";

    /**
     * 保存设备信息
     */
    public static final String SAVE_DEVICE_INFO = RECOMMEND + "/device/info";

    public static final String GET_INTEREST_TAG_LIST_STRONG = AU_PATH + "/tag/save";

    //**********************************已购列表*******************************************/
    private static final String PURCHASED_PATH_STRONG = AU_PATH + "/buy/purchased";
    public static final String PURCHASED_PATH_LIST_STRONG = PURCHASED_PATH_STRONG + "/list";
    public static final String PURCHASED_PATH_PLAY = AU_PATH + "/purchased/playlist";
    //**********************************订单列表*******************************************/
    private static final String ORDER_PATH_STRONG = AU_PATH + "/buy/order";
    public static final String ORDER_PATH_LIST_STRONG = ORDER_PATH_STRONG + "/list";

    //**********************************获取vip套餐信息列表*******************************************/
    private static final String BUY_AU_PATH_STRONG = KAOLA_VERSION + "/au/buy";
    private static final String BUY_PATH_STRONG = KAOLA_VERSION + "/buy";
    public static final String VIP_MEALS_PATH_STRONG = BUY_PATH_STRONG + "/getVipMeals";

    //**********************************生成购买vip二维码*******************************************/
    public static final String VIP_QRCODE_PATH_STRONG = BUY_AU_PATH_STRONG + "/getVipQRCode";

    //**********************************生成购买专辑二维码*******************************************/
    public static final String ALBUM_QRCODE_PATH_STRONG = BUY_AU_PATH_STRONG + "/getAlbumQRCode";

    //**********************************生成购买单曲二维码*******************************************/
    public static final String AUDIO_QRCODE_PATH_STRONG = BUY_AU_PATH_STRONG + "/getAudiosQRCode";

    //**********************************查看二维码状态*******************************************/
    public static final String QRCODE_STATUS_PATH_STRONG = BUY_PATH_STRONG + "/qrCodeStatus";

    //**********************************云币购买专辑*******************************************/
    public static final String BUY_ALBUM_BY_COIN_PATH_STRONG = BUY_AU_PATH_STRONG + "/buyAlbumByCoin";

    //**********************************云币购买单曲*******************************************/
    public static final String BUY_AUDIO_BY_COIN_PATH_STRONG = BUY_AU_PATH_STRONG + "/buyAudiosByCoin";

    //**********************************获取用户财产信息*******************************************/
    public static final String GET_USER_WEALTH = AU_PATH + "/accountBalance";

    //**********************************活动相关接口*******************************************/

    public static final String GET_ACTIVITY_INFO_LIST = KAOLA_VERSION + "/activity/infoList";

}
