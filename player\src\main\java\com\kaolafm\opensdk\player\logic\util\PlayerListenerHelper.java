package com.kaolafm.opensdk.player.logic.util;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.NetworkUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.KLAudioStateChangedByAudioFocusListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IGeneralListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayListStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class PlayerListenerHelper {
    /**
     * 播放器状态回调
     */
    private CopyOnWriteArrayList<IPlayerStateListener> mPlayControlListenerArrayList;
    /**
     * 播放器播单状态回调
     */
    private CopyOnWriteArrayList<IPlayListStateListener> mPlayListControlListenerArrayList;

    /**
     * 播放器初始化
     */
    private CopyOnWriteArrayList<IPlayerInitCompleteListener> mPlayerInitCompleteListenerArrayList;

    /**
     * 音频焦点队列
     */
    private CopyOnWriteArrayList<OnAudioFocusChangeInter> mAudioFocusListenerArrayList;

    /**
     * 通用状态回调
     */
    private CopyOnWriteArrayList<IGeneralListener> mGeneralListenerArrayList;

    /**
     * 播单变化 内部回调
     */
    private MIPlayListStateListener miPlayListStateListener;
    /**
     * 播放状态 内部回调
     */
    private MBasePlayStateListener mBasePlayStateListener;

    /**
     * 播放器初始化完成
     */
    private MPlayerInitCompleteListener mPlayerInitCompleteListener;

    /**
     * 音频焦点
     */
    private MOnAudioFocusChangeInter mOnAudioFocusChangeInter;

    /**
     * 是否因为音频焦点导致的暂停
     */
    private MAudioStateChangedByAudioFocusListener mAudioStateChangedByAudioFocusListener;

    /**
     * 播放器是否初始化成功
     */
    private boolean isPlayerInitSuccess = false;

    /**
     * 当前音频流类型
     */
    private int mAudioFocusStatus;

    private int mType;

    private PlayItem mPlayItem;

    /**
     * 是否是由用户出发操作
     */
    private volatile boolean isPauseFromUser = false;

    public PlayerListenerHelper() {
        mPlayControlListenerArrayList = new CopyOnWriteArrayList<>();
        mPlayListControlListenerArrayList = new CopyOnWriteArrayList<>();
        mPlayerInitCompleteListenerArrayList = new CopyOnWriteArrayList<>();
        mAudioFocusListenerArrayList = new CopyOnWriteArrayList<>();
        mGeneralListenerArrayList = new CopyOnWriteArrayList<>();

        miPlayListStateListener = new MIPlayListStateListener(this);
        mBasePlayStateListener = new MBasePlayStateListener(this);
        mPlayerInitCompleteListener = new MPlayerInitCompleteListener(this);
        mOnAudioFocusChangeInter = new MOnAudioFocusChangeInter(this);
        mAudioStateChangedByAudioFocusListener = new MAudioStateChangedByAudioFocusListener(this);
    }


    private void notifyListChange(List<PlayItem> playItemArrayList) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }

//        for (int i = 0, size = mPlayListControlListenerArrayList.size(); i < size; i++) {
//            IPlayListStateListener listener = mPlayListControlListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.onPlayListChange(playItemArrayList);
//        }
        //fixme 索引越界导致app崩溃
        Iterator<IPlayListStateListener> iterator = mPlayListControlListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IPlayListStateListener listener = iterator.next();
            if (listener != null) {
                listener.onPlayListChange(playItemArrayList);
            }
        }
    }

    private void notifyPlayListError(int code) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }
//        for (int i = 0, size = mPlayListControlListenerArrayList.size(); i < size; i++) {
//            IPlayListStateListener listener = mPlayListControlListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.onPlayListChangeError(code);
//        }
        //fixme 索引越界导致app崩溃
        Iterator<IPlayListStateListener> iterator = mPlayListControlListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IPlayListStateListener listener = iterator.next();
            if (listener != null) {
                listener.onPlayListChangeError(code);
            }
        }

    }

    public void notifyPlayControl(int type, PlayItem playItem) {
        notifyPlayControl(type, playItem, 0, 0);
    }

    private void notifyPlayControl(int type, PlayItem playItem, long l, long l2) {
        if (type != PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS && type != PlayerConstants.TYPE_PLAYER_PROGRESS) {
            mType = type;
            mPlayItem = playItem;
        }
        if (ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            return;
        }
//        for (int i = 0, size = mPlayControlListenerArrayList.size(); i < size; i++) {
//            IPlayerStateListener listener = mPlayControlListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//
//            notifyPlayState(listener, playItem, type, l, l2);
//        }
        Iterator<IPlayerStateListener> iterator = mPlayControlListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IPlayerStateListener listener = iterator.next();
            if (listener != null) {
                notifyPlayState(listener, playItem, type, l, l2); //l(what,即错误码),l2(extra)
            }
        }
    }

    private void notifyPlayState(IPlayerStateListener listener, PlayItem playItem, int type, long l1, long l2) {
        switch (type) {
            case PlayerConstants.TYPE_PLAYER_IDLE:
                listener.onIdle(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PREPARING:
                listener.onPlayerPreparing(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PLAYING:
                listener.onPlayerPlaying(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_PROGRESS:
                listener.onProgress(playItem, l1, l2);
                break;
            case PlayerConstants.TYPE_PLAYER_PAUSED:
                listener.onPlayerPaused(playItem);
                break;
            case PlayerConstants.TYPE_SEEK_START:
                listener.onSeekStart(playItem);
                break;
            case PlayerConstants.TYPE_SEEK_COMPLETE:
                listener.onSeekComplete(playItem);
                break;
            case PlayerConstants.TYPE_BUFFERING_START:
                listener.onBufferingStart(playItem);
                break;
            case PlayerConstants.TYPE_BUFFERING_END:
                listener.onBufferingEnd(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_END:
                listener.onPlayerEnd(playItem);
                break;
            case PlayerConstants.TYPE_PLAYER_FAILED: //error type
                listener.onPlayerFailed(playItem, (int) l1, (int) l2); //l1(error code),l2(type msg)
                break;
            case PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS:
                listener.onDownloadProgress(playItem, l1, l2);
                break;
            default:
                break;
        }
    }

    /**
     * 播放状态监听
     */
    public static class MBasePlayStateListener extends BasePlayStateListener {
        PlayerListenerHelper playerListenerHelper;

        public MBasePlayStateListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onIdle(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_IDLE, playItem));
        }

        @Override
        public void onPlayerPreparing(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PREPARING, playItem));
        }

        @Override
        public void onPlayerPlaying(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PLAYING, playItem));
        }

        @Override
        public void onPlayerPaused(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PAUSED, playItem));
        }

        @Override
        public void onProgress(PlayItem playItem, long progress, long total) {
            //   Log.i(PlayerConstants.LOG_PROGRESS_TAG, getClass().getSimpleName()+ "   progress = "+progress);
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_PROGRESS, playItem, progress, total));
        }

        @Override
        public void onPlayerFailed(PlayItem playItem, int what, int extra) { //what（error code）,extra(msg)
            playerListenerHelper.isPauseFromUser = false;
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_FAILED, playItem, what, extra));
        }

        @Override
        public void onPlayerEnd(PlayItem playItem) {
//            OnPlayEnd onPlayEnd = PlayerCustomizeManager.getInstance().getOnPlayEnd();
//            if (onPlayEnd != null) {
//                PlayerLogUtil.log(getClass().getSimpleName(),  "获取到播放结束拦截器, 通知播放结束拦截器");
//                onPlayEnd.interceptPlayEnd(playItem, () -> playEnd(playItem));
//                return;
//            }
            playEnd(playItem);
        }

        @Override
        public void onSeekStart(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_SEEK_START, playItem));

        }

        @Override
        public void onSeekComplete(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_SEEK_COMPLETE, playItem));
        }

        @Override
        public void onBufferingStart(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_BUFFERING_START, playItem));
        }

        @Override
        public void onBufferingEnd(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_BUFFERING_END, playItem));
        }

        @Override
        public void onDownloadProgress(PlayItem playItem, long progress, long total) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_DOWNLOAD_PROGRESS, playItem, progress, total));
        }

        private void playEnd(PlayItem playItem) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayControl(PlayerConstants.TYPE_PLAYER_END, playItem));
            playItem.setPosition(0);
            if (!NetworkUtil.isNetworkAvailable(PlayerManager.mContext)) {
                playerListenerHelper.notifyPlayError(-1);
                playerListenerHelper.isPauseFromUser = false;
                return;
            }
            PlayerManager.getInstance().playNext();
        }
    }


    /**
     * 播单变化监听
     */
    public static class MIPlayListStateListener implements IPlayListStateListener {
        PlayerListenerHelper playerListenerHelper;

        public MIPlayListStateListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onPlayListChange(List<PlayItem> playItemArrayList) {
            playerListenerHelper.notifyListChange(playItemArrayList);
        }

        @Override
        public void onPlayListChangeError(int errorCode) {
            playerListenerHelper.notifyPlayListError(errorCode);
        }
    }

    /**
     * 添加播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void addPlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        if (PlayerPreconditions.checkNull(mPlayListControlListenerArrayList)) {
            return;
        }
        if (mPlayListControlListenerArrayList.contains(iPlayListControlListener)) {
            return;
        }
        mPlayListControlListenerArrayList.add(iPlayListControlListener);
    }

    /**
     * 删除播单改变监听
     *
     * @param iPlayListControlListener
     */
    public void removePlayListControlStateCallback(IPlayListStateListener iPlayListControlListener) {
        if (ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            return;
        }
        if (mPlayListControlListenerArrayList.contains(iPlayListControlListener)) {
            mPlayListControlListenerArrayList.remove(iPlayListControlListener);
        }
    }

    /**
     * 添加播放状态监听
     *
     * @param iPlayControlListener
     */
    public void addPlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        if (PlayerPreconditions.checkNull(mPlayControlListenerArrayList)) {
            return;
        }
        if (mPlayControlListenerArrayList.contains(iPlayControlListener)) {
            return;
        }
        if (mType > 0 && mPlayItem != null) {
            notifyPlayState(iPlayControlListener, mPlayItem, mType, 0, 0);
        }
        mPlayControlListenerArrayList.add(iPlayControlListener);
    }

    /**
     * 删除播放状态监听
     *
     * @param iPlayControlListener
     */
    public void removePlayControlStateCallback(IPlayerStateListener iPlayControlListener) {
        if (ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            return;
        }
        if (mPlayControlListenerArrayList.contains(iPlayControlListener)) {
            mPlayControlListenerArrayList.remove(iPlayControlListener);
        }
    }

    public static class MPlayerInitCompleteListener implements IPlayerInitCompleteListener {
        PlayerListenerHelper playerListenerHelper;

        public MPlayerInitCompleteListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onPlayerInitComplete(boolean flag) {
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyPlayerInitComplete());
        }
    }

    public void addPlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        if (PlayerPreconditions.checkNull(mPlayerInitCompleteListenerArrayList)) {
            return;
        }
        if (mPlayerInitCompleteListenerArrayList.contains(iPlayerInitCompleteListener)) {
            return;
        }
        mPlayerInitCompleteListenerArrayList.add(iPlayerInitCompleteListener);
    }


    public void removePlayerInitComplete(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        if (ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            return;
        }
        if (mPlayerInitCompleteListenerArrayList.contains(iPlayerInitCompleteListener)) {
            mPlayerInitCompleteListenerArrayList.remove(iPlayerInitCompleteListener);
        }
    }

    private void notifyPlayerInitComplete() {
        isPlayerInitSuccess = true;
        if (ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            return;
        }

//        for (int i = 0, size = mPlayerInitCompleteListenerArrayList.size(); i < size; i++) {
//            IPlayerInitCompleteListener listener = mPlayerInitCompleteListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.onPlayerInitComplete(true);
//        }

        //fixme 索引越界导致app崩溃
        Iterator<IPlayerInitCompleteListener> iterator = mPlayerInitCompleteListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IPlayerInitCompleteListener listener = iterator.next();
            if (listener != null) {
                listener.onPlayerInitComplete(true);
            }
        }

    }

    public static class MOnAudioFocusChangeInter implements OnAudioFocusChangeInter {
        PlayerListenerHelper playerListenerHelper;

        public MOnAudioFocusChangeInter(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onAudioFocusChange(int focusChange) {
            PlayerLogUtil.log(getClass().getSimpleName(), "MOnAudioFocusChangeInter->onAudioFocusChange: " + focusChange);
            playerListenerHelper.mAudioFocusStatus = focusChange;
            UIThreadUtil.runUIThread(() -> playerListenerHelper.notifyAudioFocus(focusChange));
        }
    }

    public void notifyAudioFocus(int state) {
        if (ListUtil.isEmpty(mAudioFocusListenerArrayList)) {
            return;
        }
//        for (int i = 0, size = mAudioFocusListenerArrayList.size(); i < size; i++) {
//            OnAudioFocusChangeInter listener = mAudioFocusListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.onAudioFocusChange(state);
//        }
        //fixme
        Iterator<OnAudioFocusChangeInter> iterator = mAudioFocusListenerArrayList.iterator();
        while (iterator.hasNext()) {
            OnAudioFocusChangeInter listener = iterator.next();
            if (listener != null) {
                listener.onAudioFocusChange(state);
            }
        }
    }

    public void addAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(iAudioFocusListener)) {
            return;
        }
        if (mAudioFocusListenerArrayList.contains(iAudioFocusListener)) {
            return;
        }
        mAudioFocusListenerArrayList.add(iAudioFocusListener);
    }

    public void removeAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(iAudioFocusListener)) {
            return;
        }
        if (mAudioFocusListenerArrayList.contains(iAudioFocusListener)) {
            mAudioFocusListenerArrayList.remove(iAudioFocusListener);
        }
    }

    private static class MAudioStateChangedByAudioFocusListener implements KLAudioStateChangedByAudioFocusListener {
        PlayerListenerHelper playerListenerHelper;

        public MAudioStateChangedByAudioFocusListener(PlayerListenerHelper playerListenerHelper) {
            this.playerListenerHelper = playerListenerHelper;
        }

        @Override
        public void onAudioStatePausedByLossAudioFocus() {
            playerListenerHelper.isPauseFromUser = false;
        }

        @Override
        public void onAudioStatePlayingByGainAudioFocus() {
        }
    }


    public MIPlayListStateListener getPlayListStateListener() {
        return miPlayListStateListener;
    }

    public MBasePlayStateListener getBasePlayStateListener() {
        return mBasePlayStateListener;
    }

    public MPlayerInitCompleteListener getPlayerInitCompleteListener() {
        return mPlayerInitCompleteListener;
    }

    public MOnAudioFocusChangeInter getOnAudioFocusChangeInter() {
        return mOnAudioFocusChangeInter;
    }

    public MAudioStateChangedByAudioFocusListener getAudioStateChangedByAudioFocusListener() {
        return mAudioStateChangedByAudioFocusListener;
    }

    public boolean isPlayerInitSuccess() {
        return isPlayerInitSuccess;
    }

    public int getAudioFocusStatus() {
        return mAudioFocusStatus;
    }


    public void addGeneralListener(IGeneralListener generalListener) {
        if (PlayerPreconditions.checkNull(mGeneralListenerArrayList)) {
            return;
        }
        if (mGeneralListenerArrayList.contains(generalListener)) {
            return;
        }
        mGeneralListenerArrayList.add(generalListener);
    }

    public void removeGeneralListener(IGeneralListener generalListener) {
        if (PlayerPreconditions.checkNull(generalListener)) {
            return;
        }
        if (PlayerPreconditions.checkNull(mGeneralListenerArrayList)) {
            return;
        }

        if (mGeneralListenerArrayList.contains(generalListener)) {
            mGeneralListenerArrayList.remove(generalListener);
        }
    }

    public void notifyGetPlayListError(int code) {
        if (ListUtil.isEmpty(mGeneralListenerArrayList)) {
            return;
        }
//        for (int i = 0, size = mGeneralListenerArrayList.size(); i < size; i++) {
//            IGeneralListener listener = mGeneralListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.getPlayListError(code);
//        }
        Iterator<IGeneralListener> iterator = mGeneralListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IGeneralListener listener = iterator.next();
            if (listener!=null){
                listener.getPlayListError(code);
            }
        }

    }

    public void notifyPlayError(int code) {
        if (ListUtil.isEmpty(mGeneralListenerArrayList)) {
            return;
        }
//        for (int i = 0, size = mGeneralListenerArrayList.size(); i < size; i++) {
//            IGeneralListener listener = mGeneralListenerArrayList.get(i);
//            if (listener == null) {
//                continue;
//            }
//            listener.playUrlError(-1);
//        }
        //fixme
        Iterator<IGeneralListener> iterator = mGeneralListenerArrayList.iterator();
        while (iterator.hasNext()) {
            IGeneralListener listener = iterator.next();
            if (listener != null) {
                listener.playUrlError(code);
            }
        }

    }

    public boolean isPauseFromUser() {
        return isPauseFromUser;
    }

    public void setPauseFromUser(boolean pauseFromUser) {
        isPauseFromUser = pauseFromUser;
    }

    public void release() {
        if (!ListUtil.isEmpty(mPlayControlListenerArrayList)) {
            mPlayControlListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mPlayListControlListenerArrayList)) {
            mPlayListControlListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mAudioFocusListenerArrayList)) {
            mAudioFocusListenerArrayList.clear();
        }
        if (!ListUtil.isEmpty(mPlayerInitCompleteListenerArrayList)) {
            mPlayerInitCompleteListenerArrayList.clear();
        }

        if (!ListUtil.isEmpty(mGeneralListenerArrayList)) {
            mGeneralListenerArrayList.clear();
        }
    }

}
