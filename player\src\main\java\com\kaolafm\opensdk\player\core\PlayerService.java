package com.kaolafm.opensdk.player.core;

import android.app.Service;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Binder;
import android.os.IBinder;
import android.util.Log;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInner;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.AudioFocusManager;
import com.kaolafm.opensdk.player.core.utils.ContextMediaPlayer;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.util.concurrent.Callable;

import io.reactivex.Single;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;

import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK;
import static android.media.AudioManager.AUDIOFOCUS_NONE;

/**
 * <AUTHOR> on 2019-05-24.
 */

public class PlayerService extends Service {

    private ContextMediaPlayer mContextMediaPlayer;
    private String mPlayingUri;
    private boolean mIsLiving;
    private AAudioFocus mAudioFocusManager;
    private OnAudioFocusChangeInter mIAudioFocusListener;
    /**
     * 是否失去了音频焦点
     */
    private boolean isLoseAudioFocus = true;

    /**
     * 焦点变化时, 播放状态
     */
    private int mPrePlayStatus;

    /**
     * 上次焦点状态
     */
    private int mPreFocusChange = AUDIOFOCUS_NONE;
    /**
     * 当前的音频焦点
     */
    private int mCurrentAudioFocusState = AUDIOFOCUS_NONE;

    private IPlayerStateCoreListener mIPlayerStateCoreListener;

    /**
     * 是否启用淡入淡出
     */
    private boolean audioFadeEnabled = true;

    private String httpProxy = null;

    // 是否清除dns缓存
    private boolean clearDnsCache = false;

    /**
     * 淡入淡出效果配置信息
     */
    private AudioFadeConfig audioFadeConfig;

    /**
     * 异步开始播放正在执行标记
     */
    private volatile boolean mIsAsyncStartExecuting = false;

    @Override
    public IBinder onBind(Intent intent) {
        return new PlayerServiceBinder();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        initContextMediaPlayer();
    }

    private void initContextMediaPlayer() {
        mContextMediaPlayer = new ContextMediaPlayer();
    }


    public class PlayerServiceBinder extends Binder {
        public void initPlayer() {
            initPlayerInner();
        }

        public void start(String uri, long duration) {
            mIsLiving = false;
            startInner(uri, duration, 0, false);
        }

        public void start(String uri, long duration, boolean isLocalFile, boolean isLiving) {
            mIsLiving = isLiving;
            startInner(uri, duration, 0, isLocalFile);
        }

        public void start(String uri, long duration, long position, boolean isLiving) {
            mIsLiving = isLiving;
            startInner(uri, duration, position, false);
        }

        public void start(String uri, long duration, long position, boolean isLocalFile, boolean isLiving) {
            mIsLiving = isLiving;
            startInner(uri, duration, position, isLocalFile);
        }

        public void setPosition(long sec) {
            setPositionInner(sec);
        }

        public void pause() {
            pauseInner();
        }

        public void play() {
            playInner();
        }

        public void stop() {
            stopInner();
        }

        public void reset() {
            resetInner();
        }

        public void release() {
            releaseInner();
        }

        public void seek(long mSec) {
            seekInner(mSec);
        }

        public void setPlayerStateListener(IPlayerStateCoreListener iPlayerState) {
            setPlayerStateListenerInner(iPlayerState);
        }

        public void setPlayerBufferProgressListener(IPlayerBufferProgressListener listener) {
            setPlayerBufferProgressListenerInner(listener);
        }

        public void setInitCompleteListener(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
            setInitCompleteListenerInner(iPlayerInitCompleteListener);
        }

        public void setLoudnessNormalization(int active) {
            setLoudnessNormalizationInner(active);
        }

        public boolean isPlaying() {
            return isPlayingInner();
        }

        public int getPlayStatus() {
            return getPlayStatusInner();
        }

        public boolean requestAudioFocus() {
            return requestAudioFocusInner();
        }

        public boolean abandonAudioFocus() {
            return abandonAudioFocusInner();
        }

        public void setCustomAudioFocus(AAudioFocus audioFocus) {
            setCustomAudioFocusInner(audioFocus);
        }

        public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
            setAudioFocusListenerInner(iAudioFocusListener);
        }

        public long getCurrentPosition() {
            return getCurrentPositionInner();
        }

        public void setMediaVolume(float leftVolume, float rightVolume) {
            setMediaVolumeInner(leftVolume, rightVolume);
        }

        public void setLogInValid() {
            setLogInValidInner();
        }

        public void setPlayUrl(String uri, long position, long duration) {
            setPlayUrlInner(uri, position, duration);
        }

        public void disableAudioFade() {
            setAudioFadeEnabledInner(false);
        }

        public void setHttpProxy(String httpProxy) {
            setHttpProxyInner(httpProxy);
        }

        public void clearHttpProxy() {
            setHttpProxyInner(null);
        }

        public void clearDnsCache(boolean clearDnsCache) {
            setClearDnsCacheInner(clearDnsCache);
        }

        public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
            setAudioFadeConfigInner(audioFadeConfig);
        }

        public boolean isAsyncStartExecuting() {
            return mIsAsyncStartExecuting;
        }
    }

    private void initPlayerInner() {
        mContextMediaPlayer.initPlayer(ContextMediaPlayer.TYPE_IJK_MEDIA_PLAYER, this);
        mAudioFocusManager = new AudioFocusManager(PlayerService.this);
        mAudioFocusManager.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void startInner(String uri, long duration, long position, boolean isLocalFile) {
        if (StringUtil.isEmpty(uri)) {
            return;
        }

        if (mIsAsyncStartExecuting) {
            return;
        }

        mPlayingUri = uri;

        PlayerCustomizeManager playerCustomizeManager = PlayerCustomizeManager.getInstance();
        if (playerCustomizeManager.disposePlay()) {
            // 阻断之后，获取音频焦点还能从此继续播放
            setPlayUrlInner(uri, position, duration);
            return;
        }

        if (playerCustomizeManager.isDisposePlayOnce()) {//阻断一次会自动恢复不阻断
            Log.i("PlayerService", "return by disposeOnce");
            // 阻断之后，获取音频焦点还能从此继续播放
            setPlayUrlInner(uri, position, duration);
            return;
        }

        if (!checkAudioFocus()) {
            setPlayUrlInner(uri, position, duration);
            return;
        }

        int streamTypeChannel = playerCustomizeManager.getStreamChannel();
        synchronized (PlayerService.class) {
            PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "async start executing.");
            mIsAsyncStartExecuting = true;
            Single.fromCallable(new Callable<Boolean>() {
                @Override
                public Boolean call() throws Exception {
                    mContextMediaPlayer.getMediaPlayer().start(uri, duration, position, streamTypeChannel, audioFadeEnabled, audioFadeConfig, httpProxy, clearDnsCache);
                    return true;
                }
            }).subscribeOn(Schedulers.io()).subscribe(new Consumer<Boolean>() {
                @Override
                public void accept(Boolean aBoolean) throws Exception {
                    synchronized (PlayerService.class) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "startInner", "async start executing end.");
                        mIsAsyncStartExecuting = false;
                    }
                }
            });
        }
    }

    private void setPositionInner(long sec) {
        mContextMediaPlayer.getMediaPlayer().seek(sec);
    }

    private void pauseInner() {
        mContextMediaPlayer.getMediaPlayer().pause();
    }

    private void playInner() {
        if (PlayerCustomizeManager.getInstance().disposePlay()) {
            return;
        }
        if (!checkAudioFocus()) {
            return;
        }
        mContextMediaPlayer.getMediaPlayer().play();
    }

    private void stopInner() {
        mContextMediaPlayer.getMediaPlayer().stop();
    }

    private void resetInner() {
        mContextMediaPlayer.getMediaPlayer().reset();
    }

    private void releaseInner() {
        mContextMediaPlayer.getMediaPlayer().release();
    }

    private void seekInner(long mSec) {
        mContextMediaPlayer.getMediaPlayer().seek(mSec);
    }

    private void setPlayerStateListenerInner(IPlayerStateCoreListener iPlayerState) {
        mIPlayerStateCoreListener = iPlayerState;
        mContextMediaPlayer.getMediaPlayer().setPlayerStateListener(iPlayerStateCoreListener);
    }

    private void setPlayerBufferProgressListenerInner(IPlayerBufferProgressListener listener) {
        mContextMediaPlayer.getMediaPlayer().setBufferProgressListener(listener);
    }

    private void setInitCompleteListenerInner(IPlayerInitCompleteListener iPlayerInitCompleteListener) {
        mContextMediaPlayer.getMediaPlayer().setInitPlayerInitCompleteListener(iPlayerInitCompleteListener);
    }

    private void setLoudnessNormalizationInner(int active) {
        mContextMediaPlayer.getMediaPlayer().setLoudnessNormalization(active);
    }

    private boolean isPlayingInner() {
        return mContextMediaPlayer.getMediaPlayer().isPlaying();
    }

    private void setCustomAudioFocusInner(AAudioFocus audioFocus) {
        if (audioFocus == null) {
            return;
        }
        mAudioFocusManager = audioFocus;
        audioFocus.setAudioFocusListener(onAudioFocusChangeInner);
    }

    private void setAudioFocusListenerInner(OnAudioFocusChangeInter iAudioFocusListener) {
        mIAudioFocusListener = iAudioFocusListener;
    }

    private boolean requestAudioFocusInner() {
        return mAudioFocusManager.requestAudioFocus();
    }

    private boolean abandonAudioFocusInner() {
        return mAudioFocusManager.abandonAudioFocus();
    }

    private void managerAudioFocusChange(int focusChange) {
        PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "focusChange = " + focusChange);
        if (focusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
            if (isPlayingInner()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "is playing");
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
                if (!PlayerCustomizeManager.getInstance().disposeAudioFocusChangeDuck()) {
                    setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MIN, PlayerConstants.RIGHT_VOLUME_MIN);
                }
            } else {
                PlayerLogUtil.log(getClass().getSimpleName(), "managerAudioFocusChange", "is not playing");
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                if (mPreFocusChange != focusChange) {
                    mPrePlayStatus = getPlayStatusInner();
                }
            }
        } else if (focusChange == AudioManager.AUDIOFOCUS_GAIN) {
            manageGainFocus();
        } else if (focusChange == AUDIOFOCUS_LOSS || focusChange == AUDIOFOCUS_LOSS_TRANSIENT) {
            manageLossFocus();
        }

        mPreFocusChange = focusChange;
    }

    private void manageGainFocus() {
        boolean isPauseFromUser = PlayerManager.getInstance().isPauseFromUser();
        PlayerLogUtil.log(getClass().getSimpleName(), "manageGainFocus", "mPreFocusChange = " + mPreFocusChange + ", mPrePlayStatus = " + mPrePlayStatus + ", isPauseFromUser = " + isPauseFromUser);
        //当重新获取焦点后, 检测是否是用户暂停
        if (isPauseFromUser) {
            return;
        }
        if (mPrePlayStatus == PlayerConstants.TYPE_PLAYER_PLAYING) {
            if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT || mPreFocusChange == AUDIOFOCUS_LOSS) {
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
                playInner();
                PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(true);
            } else if (mPreFocusChange == AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK) {
                setMediaVolumeInner(PlayerConstants.LEFT_VOLUME_MAX, PlayerConstants.RIGHT_VOLUME_MAX);
            }
        }
    }

    private void manageLossFocus() {
        boolean isPlaying = isPlayingInner();
        PlayerLogUtil.log(getClass().getSimpleName(), "manageLossFocus", "isPlaying = " + isPlaying);
        if (isPlaying) {
            pauseInner();
            PlayerCustomizeManager.getInstance().notifyPlayerStateChangedByAudioFocus(false);
        }
    }

    /**
     * 检查音频焦点
     *
     * @return
     */
    private boolean checkAudioFocus() {
        PlayerLogUtil.log(getClass().getSimpleName(), "checkAudioFocus", "isLoseAudioFocus = " + isLoseAudioFocus);
        if (!PlayerCustomizeManager.getInstance().isNeedRequestAudioFocus()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "checkAudioFocus", "no need");
            return true;
        }
        if (isLoseAudioFocus && !requestAudioFocusInner()) {
            return false;
        }
        return true;
    }

    private void saveLostAudioFocusBeforePlayState(int focusChange) {
        if (focusChange != AUDIOFOCUS_LOSS && focusChange != AUDIOFOCUS_LOSS_TRANSIENT) {
            return;
        }
        if (isPlayingInner()) {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
        } else {
            mPrePlayStatus = PlayerConstants.TYPE_PLAYER_IDLE;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "saveLostAudioFocusBeforePlayState", "mPrePlayStatus=" + mPrePlayStatus);
    }

    private OnAudioFocusChangeInner onAudioFocusChangeInner = new OnAudioFocusChangeInner() {
        @Override
        public void onAudioFocusChange(boolean isUseBySystem, int focusChange) {
            PlayerLogUtil.log(getClass().getSimpleName(), "onAudioFocusChangeInner", "focusChange =" + focusChange);
            isLoseAudioFocus = focusChange != AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
            mCurrentAudioFocusState = focusChange;
            saveLostAudioFocusBeforePlayState(focusChange);
            if (mIAudioFocusListener != null) {
                mIAudioFocusListener.onAudioFocusChange(focusChange);
            }

            if (isUseBySystem) {
                managerAudioFocusChange(focusChange);
            }
        }
    };

    /**
     * 获取播放状态
     *
     * @return
     */
    private int getPlayStatusInner() {
        return mContextMediaPlayer.getMediaPlayer().getPlayStatus();
    }

    /**
     * 获取当前进度
     *
     * @return
     */
    private long getCurrentPositionInner() {
        return mContextMediaPlayer.getMediaPlayer().getCurrentPosition();
    }

    /**
     * 设置音量
     *
     * @param leftVolume
     * @param rightVolume
     */
    private void setMediaVolumeInner(float leftVolume, float rightVolume) {
        mContextMediaPlayer.getMediaPlayer().setMediaVolume(leftVolume, rightVolume);
    }

    private void setLogInValidInner() {
        mContextMediaPlayer.getMediaPlayer().setLogInValid();
    }

    private void setPlayUrlInner(String uri, long position, long duration) {
        PlayerLogUtil.log(getClass().getSimpleName(), "setPlayUrlInner", "url= " + uri + "position = " + position + " duration = " + duration);

        mContextMediaPlayer.getMediaPlayer().reset();

        mContextMediaPlayer.getMediaPlayer().setAutoPlayOnPrepared(false);
        // 启用音频淡入淡出效果
        //mContextMediaPlayer.getMediaPlayer().setAudioFadeEnabled(audioFadeEnabled);
        //mContextMediaPlayer.getMediaPlayer().setAudioFadeConfig(audioFadeConfig);
        mContextMediaPlayer.getMediaPlayer().setDataSource(uri);

        if (duration > 0) {
            mContextMediaPlayer.getMediaPlayer().setDuration(duration, duration);
        }

        if (position > 0) {
            mContextMediaPlayer.getMediaPlayer().seekAtStart(position);
        }
        mContextMediaPlayer.getMediaPlayer().prepare(1);
    }

    private IPlayerStateCoreListener iPlayerStateCoreListener = new IPlayerStateCoreListener() {
        @Override
        public void onIdle(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onIdle(url);
            }
        }

        @Override
        public void onPlayerPreparingComplete(String url) {
            PlayerLogUtil.log(getClass().getSimpleName(), "onPlayerPreparingComplete", "mCurrentAudioFocusState = " + mCurrentAudioFocusState);
            if (mCurrentAudioFocusState == AUDIOFOCUS_LOSS || mCurrentAudioFocusState == AUDIOFOCUS_LOSS_TRANSIENT) {
                if (mIsLiving) {
                    resetInner();
                } else {
                    pauseInner();
                }
                mPrePlayStatus = PlayerConstants.TYPE_PLAYER_PLAYING;
                return;
            }
            mContextMediaPlayer.getMediaPlayer().play();
        }

        @Override
        public void onPlayerPreparing(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPreparing(url);
            }
        }

        @Override
        public void onPlayerPlaying(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPlaying(url);
            }
        }

        @Override
        public void onPlayerPaused(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerPaused(url);
            }
        }

        @Override
        public void onProgress(String url, long progress, long total) {
            // 正在起播过程中，不上报进度，避免进度条跳动
            if (mIsAsyncStartExecuting) {
                return;
            }
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onProgress(url, progress, total);
            }
        }

        @Override
        public void onPlayerFailed(String url, int what, int extra, String dnsAddress) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerFailed(url, what, extra, dnsAddress);
            }
        }

        @Override
        public void onPlayerEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onPlayerEnd(url);
            }
        }

        @Override
        public void onSeekStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekStart(url);
            }
        }

        @Override
        public void onSeekComplete(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onSeekComplete(url);
            }
        }

        @Override
        public void onBufferingStart(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingStart(url);
            }
        }

        @Override
        public void onBufferingEnd(String url) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onBufferingEnd(url);
            }
        }

        @Override
        public void onInteractionFired(String url, int position, int id) {
            if (mIPlayerStateCoreListener != null) {
                mIPlayerStateCoreListener.onInteractionFired(url, position, id);
            }
        }
    };

    private void setAudioFadeEnabledInner(boolean audioFadeEnabled) {
        this.audioFadeEnabled = audioFadeEnabled;
    }

    private void setHttpProxyInner(String httpProxy) {
        this.httpProxy = httpProxy;
    }

    private void setClearDnsCacheInner(boolean clearDnsCache) {
        this.clearDnsCache = clearDnsCache;
    }

    private void setAudioFadeConfigInner(AudioFadeConfig audioFadeConfig) {
        this.audioFadeConfig = audioFadeConfig;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
