package com.kaolafm.opensdk.account.profile;

import android.app.Application;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.support.annotation.RestrictTo;
import android.text.TextUtils;

import com.kaolafm.base.internal.DeviceId;
import com.kaolafm.base.utils.DeviceUtil;
import com.kaolafm.opensdk.Options;
import com.kaolafm.opensdk.di.scope.AppScope;
import com.kaolafm.opensdk.http.urlmanager.UrlManager;
import com.kaolafm.opensdk.utils.BaseHttpsStrategy;

import javax.inject.Inject;

/**
 * profile管理类基类
 * <AUTHOR>
 * @date 2020/6/4
 */
public abstract class AbstractProfileManager<T extends Profile, O extends Options> {

    @Inject
    @AppScope
    public Application mApplication;

    @Inject
    @AppScope
    public UrlManager mUrlManager;

    @Inject
    public T mProfile;

    @Inject
    public O options;

    @RestrictTo(RestrictTo.Scope.LIBRARY)
    public void loadProfile() {
        loadProfileFromManifest();
        setProfile();
    }

    protected void setProfile() {
        if (options.isUseHttps(BaseHttpsStrategy.MEDIA)) {
            mProfile.setCapabilities(mProfile.getCapabilities() + ",MEDIA_URL_MUST_BE_HTTPS");
        }

    }

    protected void loadProfileFromManifest() {
        try {
            ApplicationInfo applicationInfo = mApplication.getPackageManager().getApplicationInfo(mApplication.getPackageName(), PackageManager.GET_META_DATA);
            Bundle metaData = applicationInfo.metaData;
            setupFromManifest(metaData);
        } catch (PackageManager.NameNotFoundException | NullPointerException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取清单文件配置信息并设置给profile
     * @param metaData
     */
    protected abstract void setupFromManifest(Bundle metaData);

    public T getProfile() {
        return mProfile;
    }

    public void setLongitude(String longitude) {
        mProfile.setLng(longitude);
    }

    public void setLatitude(String latitude) {
        mProfile.setLat(latitude);
    }

    public String getDeviceId() {
        return TextUtils.isEmpty(mProfile.getDeviceId()) ? DeviceUtil.getDeviceId(mApplication) : mProfile.getDeviceId();
    }

    public void setDeviceId(String deviceId) {
        mProfile.setDeviceId(deviceId);
    }

}
