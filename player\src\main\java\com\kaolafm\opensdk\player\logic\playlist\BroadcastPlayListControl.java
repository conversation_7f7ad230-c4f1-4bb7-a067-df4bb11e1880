package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastDetails;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.ISonPlayList;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/19.
 */

public class BroadcastPlayListControl extends BasePlayListControl implements ISonPlayList {
    private ArrayList<PlayItem> mSongPlayItemArrayList;
    private boolean isPlaySongList = false;
    private int mSongListPosition = -1;

    private BroadcastRequest mBroadcastRequest;

    public BroadcastPlayListControl() {
        mSongPlayItemArrayList = new ArrayList<>();
        mBroadcastRequest = new BroadcastRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        super.initPlayList(playerBuilder, iPlayListGetListener);
        initBroadcastInfo(iPlayListGetListener);
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
    }


    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
    }

    private void initBroadcastInfo(IPlayListGetListener iPlayListGetListener) {
        long albumId = string2Long(mPlaylistInfo.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","broadcast id =" + albumId);

        mBroadcastRequest.getBroadcastDetails(albumId, new HttpCallback<BroadcastDetails>() {
            @Override
            public void onSuccess(BroadcastDetails broadcastDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","success");
                if (PlayerPreconditions.checkNull(broadcastDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","success, list is empty");
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                mPlaylistInfo.setBroadcastChannel(broadcastDetails.getFreq());

                loadPlayList(albumId, null, new IDataListCallback<List<ProgramDetails>>() {
                    @Override
                    public void success(List<ProgramDetails> programDetails) {
                        PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","get play list success");
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.programDetailsToPlayItem(programDetails, mPlaylistInfo.getBroadcastChannel());
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","get play list success, list is empty");
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(playItemArrayList, iPlayListGetListener);
                        updatePlayListInfo((BroadcastPlayItem) playItemArrayList.get(mPosition));
                    }

                    @Override
                    public void error() {
                        PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","get play list error");
                        notifyPlayListGetError(iPlayListGetListener, -1);
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initBroadcastInfo","error");
                notifyPlayListGetError(iPlayListGetListener, -1);
            }
        });
    }

    /**
     * 加载信息
     *
     * @param albumId
     * @param data
     * @param iDataListCallback
     */
    private void loadPlayList(long albumId, String data, IDataListCallback<List<ProgramDetails>> iDataListCallback) {
        mBroadcastRequest.getBroadcastProgramList(albumId, data, new HttpCallback<List<ProgramDetails>>() {
            @Override
            public void onSuccess(List<ProgramDetails> programDetails) {
                if (ListUtil.isEmpty(programDetails)) {
                    if (iDataListCallback != null) {
                        iDataListCallback.error();
                    }
                    return;
                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(programDetails);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error();
                }
            }
        });
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition > 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem","is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(--mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition - 1 >= mPlayItemArrayList.size()) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition - 1 < 0) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem","current page is start");
                if (hasPrePage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem","has pre page");
                    loadPrePage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem","position = " + mPosition);
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition - 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getPrePlayItem","is play back");
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition - 1), null);
            } else {
                if(playItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
                    notifyPlayListGetError(iPlayListGetListener, 404);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(playItem, iPlayListGetListener);
            }
        }
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList) {
            if (mSongListPosition < mSongPlayItemArrayList.size() - 1) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","is son play list");
                notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(++mSongListPosition), null);
            }
        } else {
            if (PlayerPreconditions.checkNull(mPlayItemArrayList)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition + 1 < 0) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (mPosition + 1 >= mPlayItemArrayList.size()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","current page is end");
                if (hasNextPage()) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","has next page");
                    loadNextPage(iPlayListGetListener);
                } else {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                }
                return;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","position = " + mPosition);
            BroadcastPlayItem playItem = (BroadcastPlayItem) (mPlayItemArrayList.get(mPosition + 1));
            if (PlayerPreconditions.checkNull(playItem)) {
                notifyPlayListGetError(iPlayListGetListener, -1);
                return;
            }
            if (playItem.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","is play back");
                notifyPlayListGet(iPlayListGetListener, mPlayItemArrayList.get(mPosition + 1), null);
            } else {
                if(playItem.getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
                    notifyPlayListGetError(iPlayListGetListener, PlayerConstants.ERROR_PROGRAM_TIME_EXCEED);
                    return;
                }
                setAutoPlay(mPlayItemArrayList.get(mPosition), playItem);
                initLiving(playItem, iPlayListGetListener);
            }
        }
    }

    private void setAutoPlay(PlayItem prePlayItem, PlayItem playItem) {
        if (prePlayItem == null || playItem == null) {
            return;
        }
        String autoPlay = prePlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (!StringUtil.isEmpty(autoPlay)) {
            prePlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            prePlayItem.setPosition(0);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, autoPlay);
        }
    }

    private void initLiving(PlayItem playItem, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initLiving","get living info id: " + playItem.getAudioId());
        mBroadcastRequest.getBroadcastProgramDetails(playItem.getAudioId(), new HttpCallback<ProgramDetails>() {
            @Override
            public void onSuccess(ProgramDetails programDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving","get detail success");
                if (PlayerPreconditions.checkNull(programDetails)) {
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving","get detal success status: " + programDetails.getStatus());
                BroadcastPlayItem broadcastPlayItem = (BroadcastPlayItem) playItem;
                broadcastPlayItem.setStatus(PlayerConstants.BROADCAST_STATUS_LIVING);
                broadcastPlayItem.getTimeInfoData().setStartTime(programDetails.getStartTime());
                broadcastPlayItem.getTimeInfoData().setFinishTime(programDetails.getFinishTime());
                broadcastPlayItem.getTimeInfoData().setBeginTime(programDetails.getBeginTime());
                broadcastPlayItem.getTimeInfoData().setEndTime(programDetails.getEndTime());
                broadcastPlayItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving","success, old url = " + playItem.getPlayUrl() + " , new url= " + programDetails.getPlayUrl());
                broadcastPlayItem.setPlayUrl(programDetails.getPlayUrl());
                notifyPlayListGet(iPlayListGetListener, playItem, null);
            }

            @Override
            public void onError(ApiException exception) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initLiving","error");
                notifyPlayListGetError(iPlayListGetListener, -1);
            }
        });
    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void updatePlayListContent(ArrayList<PlayItem> playItemArrayList, IPlayListGetListener iPlayListGetListener) {
        mPlayItemArrayList.addAll(playItemArrayList);
        int index = PlayListUtils.getLivingBroadcastPlayItem(playItemArrayList);
        if (index >= playItemArrayList.size()) {
            index = 0;
        }
        mPosition = index;
        notifyPlayListGet(iPlayListGetListener, playItemArrayList.get(index), playItemArrayList);
        notifyPlayListChange(playItemArrayList);
    }

    private void updatePlayListInfo(BroadcastPlayItem broadcastPlayItem) {
        if (broadcastPlayItem != null) {
            mPlaylistInfo.setAlbumName(broadcastPlayItem.getInfoData().getAlbumName());
            mPlaylistInfo.setAlbumPic(broadcastPlayItem.getInfoData().getAlbumPic());
        }
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        long tempId = string2Long(playerBuilder.getId());
        if (isPlaySongList) {
            for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
                PlayItem playItem = mSongPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItem)) {
                    continue;
                }
                if (playItem.getAudioId() == tempId) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getPlayItem","son play list has id");
                    mSongListPosition = i;
                    isPlaySongList = true;
                    PlayItem playItem1 = mSongPlayItemArrayList.get(mSongListPosition);
                    if (playItem1 != null) {
                        playItem1.setPosition(0);
                    }
                    return playItem1;
                }
            }
        }
        isPlaySongList = false;
        mSongPlayItemArrayList.clear();

        BroadcastPlayItem playItem = (BroadcastPlayItem) super.getPlayItem(playerBuilder);
        if (PlayerPreconditions.checkNull(playItem)) {
            return null;
        }
        if (playItem.getTimeInfoData().getFinishTime() < DateUtil.getServerTime()) {
            playItem.setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
        }
        playItem.getTimeInfoData().setCurSystemTime(DateUtil.getServerTime());
        playItem.setPosition(0);
        return playItem;
    }


    @Override
    public ArrayList getSongPlayList() {
        return mSongPlayItemArrayList;
    }

    @Override
    public void addSongPlayItem(Object o) {
        mSongPlayItemArrayList.addAll((ArrayList<PlayItem>) o);
    }

    @Override
    public void removeSongPlayItem(Object o) {
        mSongPlayItemArrayList.clear();
    }

    @Override
    public boolean isPlayingSonList() {
        return isPlaySongList;
    }

    @Override
    public boolean isExistPlayItem(long id) {
        boolean isExist = super.isExistPlayItem(id);
        if (isExist) {
            return true;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem","father play list not has");
        if (PlayerPreconditions.checkNull(mSongPlayItemArrayList)) {
            return false;
        }
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem playItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }
            if (playItem.getAudioId() == id) {
                PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem","son play list, position = " + i);
                mSongListPosition = i;
                isPlaySongList = true;
                return true;
            }
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "isExistPlayItem","son play list not has");
        return false;
    }

    @Override
    public boolean hasNext() {
        if (isPlaySongList) {
            if (mSongListPosition + 1 < 0) {
                return false;
            }
            if (mSongListPosition + 1 < mPlayItemArrayList.size()) {
                return true;
            }
            return false;
        }
        return super.hasNext();
    }

    @Override
    public void release() {
        super.release();
        if (mSongPlayItemArrayList != null) {
            mSongPlayItemArrayList.clear();
            isPlaySongList = false;
            mSongListPosition = -1;
        }
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (isPlaySongList) {
            for (int i = 0; i < mPlayItemArrayList.size(); i++) {
                PlayItem playItemTemp = mPlayItemArrayList.get(i);
                if (PlayerPreconditions.checkNull(playItemTemp)) {
                    continue;
                }
                if (playItemTemp.getAudioId() == playItem.getAudioId()) {
                    playItem.setPosition(0);
                    mSongListPosition = i;
                    PlayerLogUtil.log(getClass().getSimpleName(), "setCurPosition","position: " + mSongListPosition);
                    return;
                }
            }
        } else {
            if(((BroadcastPlayItem)playItem).getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
                notifyPlayListChangeError(404);
                return;
            }
            super.setCurPosition(playItem);
        }
    }

    @Override
    public int getCurPosition() {
        if(isPlaySongList){
            return mSongListPosition;
        }
        return super.getCurPosition();
    }
}
