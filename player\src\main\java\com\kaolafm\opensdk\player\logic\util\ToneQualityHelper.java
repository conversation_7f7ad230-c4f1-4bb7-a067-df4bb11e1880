package com.kaolafm.opensdk.player.logic.util;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.ArrayList;

import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.ToneQuality.MIDDLE_TONE_QUALITY;

/******************************************
 * 类描述： Kradio 音质设置. 类名称：ToneQualityHelper
 *
 * @version: 1.0
 * @author: shaoningYang
 * @time: 2016-11-17 10:50
 ******************************************/
public class ToneQualityHelper {
    public static final String TONE_PREFERENCE_NAME = "tone_set_sp";
    /**
     * 音质设置值
     */
    public static final String TONE_VALUE = "tonevalue";

    private int mToneQuality = MIDDLE_TONE_QUALITY;

    private SharedPreferences sharedPreferenceUtil;

    private ArrayList<IToneQualityListener> mIToneQualityListenerArrayList;

    private ToneQualityHelper() {
        mIToneQualityListenerArrayList = new ArrayList<>();
    }

    private static class TONE_CLASS_INSTANCE {
        private final static ToneQualityHelper TONE_QUALITY_HELPER = new ToneQualityHelper();
    }

    /**
     * 获取音频音质帮助实例
     *
     * @return
     */
    public static ToneQualityHelper getInstance() {
        return TONE_CLASS_INSTANCE.TONE_QUALITY_HELPER;
    }

    public void initToneSetValue(Context context) {
        sharedPreferenceUtil = context.getSharedPreferences(TONE_PREFERENCE_NAME, Context.MODE_PRIVATE);
        mToneQuality = sharedPreferenceUtil.getInt(TONE_VALUE, MIDDLE_TONE_QUALITY);
    }

    public void setToneQuality(int quality) {
        mToneQuality = quality;
        if (sharedPreferenceUtil == null) {
            return;
        }

        sharedPreferenceUtil.edit().putInt(TONE_VALUE, mToneQuality).apply();
        notifyToneChange();
    }

    public int getToneQuality() {
        return mToneQuality;
    }

    public void registerToneQualityListener(IToneQualityListener iToneQualityListener) {
        if (mIToneQualityListenerArrayList.contains(iToneQualityListener)) {
            return;
        }
        mIToneQualityListenerArrayList.add(iToneQualityListener);
    }

    public void removeToneQualityListener(IToneQualityListener iToneQualityListener) {
        if (mIToneQualityListenerArrayList.contains(iToneQualityListener)) {
            mIToneQualityListenerArrayList.remove(iToneQualityListener);
        }
    }

    private void notifyToneChange() {
        for (int i = 0, size = mIToneQualityListenerArrayList.size(); i < size; i++) {
            IToneQualityListener iToneQualityListener = mIToneQualityListenerArrayList.get(i);
            if (iToneQualityListener == null) {
                continue;
            }
            iToneQualityListener.toneChange(mToneQuality);
        }
    }

    public interface IToneQualityListener {
        void toneChange(int value);
    }

}
