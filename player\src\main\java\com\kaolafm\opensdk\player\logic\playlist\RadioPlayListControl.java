package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.media.RadioRequest;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.api.media.model.RadioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.RadioPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.ISonPlayList;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class RadioPlayListControl extends BasePlayListControl implements ISonPlayList {
    //adZoneChooseType：int：1，表示广告位ID配置在AI电台详情中；2，表示广告位配置在编排位中。
    public static final int TYPE_ZONE_CHOOSE_DETAILS = 1;
    public static final int TYPE_ZONE_CHOOSE_AUDIO = 2;

    private ArrayList<PlayItem> mSongPlayItemArrayList;
    private boolean isPlaySongList = false;
    private int mSongListPosition = -1;
    private RadioRequest mRadioRequest;
    private PlayerBuilder mTempPlayerBuilder;

    public RadioPlayListControl() {
        super();
        mSongPlayItemArrayList = new ArrayList<>();
        mRadioRequest = new RadioRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList");
        mTempPlayerBuilder = playerBuilder;
        initRadioInfo(iPlayListGetListener);
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        loadData(iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage");
        loadData(iPlayListGetListener);
    }

    /**
     * 获取播单数据
     *
     * @param radioId
     * @param clockId
     * @param iDataListCallback
     */
    private void loadPlayListData(long radioId, String clockId, IDataListCallback<List<AudioDetails>> iDataListCallback) {
        mRadioRequest.getPlaylist(radioId, clockId, new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                if (ListUtil.isEmpty(audioDetails)) {
                    if (iDataListCallback != null) {
                        iDataListCallback.error();
                    }
                    return;
                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(audioDetails);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error();
                }
            }
        });
    }

    private void loadData(final IPlayListGetListener iPlayListGetListener) {
        long albumId = string2Long(mPlaylistInfo.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "loadData", "PGC loadData albumId = " + albumId);

        mRadioRequest.getPlaylist(albumId, mPlaylistInfo.getPageIndex(), new HttpCallback<List<AudioDetails>>() {
            @Override
            public void onSuccess(List<AudioDetails> audioDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadData", "PGC loadData success");

                if (PlayerPreconditions.checkNull(audioDetails)) {
                    notifyPlayListChangeError(-1);
                    return;
                }
                updateListInfo(audioDetails);
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToRadioPlayItem(audioDetails, mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListChangeError(-1);
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "loadData", "PGC loadData success size = " + playItemArrayList.size());
                mPlayItemArrayList.addAll(playItemArrayList);
                notifyPlayListChange(playItemArrayList);
                int size = mPlayItemArrayList.size() - playItemArrayList.size();
                notifyPlayListGet(iPlayListGetListener, size, playItemArrayList.get(0), playItemArrayList);
            }

            @Override
            public void onError(ApiException e) {
                notifyPlayListChangeError(e.getCode());
                notifyPlayListGetError(iPlayListGetListener, e.getCode());
            }
        });
    }

    private void initRadioInfo(final IPlayListGetListener iPlayListGetListener) {
        long radioId = string2Long(mTempPlayerBuilder.getId());
        PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "radioId = " + radioId);

        mRadioRequest.getRadioDetails(radioId, new HttpCallback<RadioDetails>() {
            @Override
            public void onSuccess(RadioDetails radioDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "get detail success");
                if (PlayerPreconditions.checkNull(radioDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "get detail empty");
                    iPlayListGetListener.onDataGetError(-1);
                    return;
                }

                loadPlayListData(radioId, "", new IDataListCallback<List<AudioDetails>>() {
                    @Override
                    public void success(List<AudioDetails> audioDetails) {
                        if (ListUtil.isEmpty(audioDetails)) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "get play list success, data is null");
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "get play list success");
                        RadioPlayListControl.super.initPlayList(mTempPlayerBuilder, iPlayListGetListener);
                        updatePlayListInfo(radioDetails, audioDetails);
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToRadioPlayItem(audioDetails, mPlaylistInfo);
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(playItemArrayList, iPlayListGetListener);
                    }

                    @Override
                    public void error() {
                        PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "get play list error");
                        notifyPlayListGetError(iPlayListGetListener, -1);
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "initRadioInfo", "error");
                notifyPlayListGetError(iPlayListGetListener, e.getCode());
            }
        });
    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void updateListInfo(List<AudioDetails> audioDetailsList) {
        if (ListUtil.isEmpty(audioDetailsList)) {
            return;
        }
        int size = audioDetailsList.size();
        AudioDetails audioDetails = audioDetailsList.get(size - 1);

        if (PlayerPreconditions.checkNull(audioDetails)) {
            return;
        }
        mPlaylistInfo.setPageIndex(audioDetails.getClockId());
        mPlaylistInfo.setHasNextPage(audioDetails.getHasNextPage() == PlayerConstants.HAVE_PAGE_DATA);
    }

    @Override
    public ArrayList<PlayItem> getSongPlayList() {
        return mSongPlayItemArrayList;
    }

    @Override
    public void addSongPlayItem(Object o) {
        if (o instanceof PlayItem) {
            int size = mSongPlayItemArrayList.size();
            PlayItem playItem = (PlayItem) o;
            for (int i = 0; i < size; i++) {
                PlayItem tempPlayItem = mSongPlayItemArrayList.get(i);
                if (tempPlayItem == null) {
                    continue;
                }
                if (tempPlayItem.getAudioId() == playItem.getAudioId()) {
                    return;
                }
            }
            mSongPlayItemArrayList.add((PlayItem) o);
        }
    }

    @Override
    public void removeSongPlayItem(Object o) {
        if (o instanceof PlayItem) {
            int size = mSongPlayItemArrayList.size();
            PlayItem playItem = (PlayItem) o;
            for (int i = 0; i < size; i++) {
                PlayItem tempPlayItem = mSongPlayItemArrayList.get(i);
                if (tempPlayItem == null) {
                    continue;
                }
                if (tempPlayItem.getAudioId() == playItem.getAudioId()) {
                    mSongPlayItemArrayList.remove(tempPlayItem);
                    return;
                }
            }
        }
    }

    @Override
    public boolean isPlayingSonList() {
        return isPlaySongList;
    }

    @Override
    public PlayItem getPlayItem(PlayerBuilder playerBuilder) {
        PlayItem playItem = super.getPlayItem(playerBuilder);
        if (playItem != null) {
            isPlaySongList = false;
            updateSonPlayList();
            return playItem;
        }
        String id = playerBuilder.getId();
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem tempPlayItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(tempPlayItem)) {
                continue;
            }
            if (String.valueOf(tempPlayItem.getAudioId()).equals(id)) {
                isPlaySongList = true;
                mSongListPosition = i;
                tempPlayItem.setPosition(0);
                return tempPlayItem;
            }
        }
        return null;
    }

    private void updateSonPlayList() {
        for (int i = 0; i < mSongPlayItemArrayList.size(); i++) {
            PlayItem tempPlayItem = mSongPlayItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(tempPlayItem)) {
                continue;
            }
            tempPlayItem.setPosition(0);
        }
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (isPlaySongList && mSongListPosition < mSongPlayItemArrayList.size() - 1) {
            PlayerLogUtil.log(getClass().getSimpleName(), "getNextPlayItem","current play list is son");
            notifyPlayListGet(iPlayListGetListener, mSongPlayItemArrayList.get(++mSongListPosition), null);
            return;
        }
        isPlaySongList = false;
        mSongListPosition = -1;
        super.getNextPlayItem(iPlayListGetListener);
    }

    @Override
    public void setCurPosition(PlayItem playItem) {
        if (isPlaySongList) {
            int size = mSongPlayItemArrayList.size();
            for (int i = 0; i < size; i++) {
                PlayItem tempPlayItem = mSongPlayItemArrayList.get(i);
                if (tempPlayItem == null) {
                    continue;
                }
                if (tempPlayItem.getAudioId() == playItem.getAudioId()) {
                    mSongListPosition = i;
                    return;
                }
            }
        }
        isPlaySongList = false;
        super.setCurPosition(playItem);
    }

    @Override
    public PlayItem getCurPlayItem() {
        if (isPlaySongList) {
            if (mSongListPosition < mSongPlayItemArrayList.size()) {
                return mSongPlayItemArrayList.get(mSongListPosition);
            }
        }
        isPlaySongList = false;
        return super.getCurPlayItem();
    }


    private void updatePlayListInfo(RadioDetails radioDetails, List<AudioDetails> audioDetailsList) {
        if (radioDetails != null) {
            mPlaylistInfo.setAlbumName(radioDetails.getName());
            mPlaylistInfo.setAlbumPic(radioDetails.getImg());
            mPlaylistInfo.setRadioType(radioDetails.getRadioType());
            mPlaylistInfo.setAdZoneId(radioDetails.getAdZoneId());
            mPlaylistInfo.setAdZoneChooseType(radioDetails.getAdZoneChooseType());
        }
        if (ListUtil.isEmpty(audioDetailsList)) {
            return;
        }
        int size = audioDetailsList.size();
        AudioDetails audioDetails = audioDetailsList.get(size - 1);

        if (PlayerPreconditions.checkNull(audioDetails)) {
            return;
        }
        mPlaylistInfo.setPageIndex(audioDetails.getClockId());
        mPlaylistInfo.setHasNextPage(audioDetails.getHasNextPage() == PlayerConstants.HAVE_PAGE_DATA);
    }


    private void updatePlayListContent(ArrayList<PlayItem> playItems, IPlayListGetListener iPlayListGetListener) {
        mPlayItemArrayList.addAll(playItems);
        notifyPlayListGet(iPlayListGetListener, playItems.get(0), playItems);
        notifyPlayListChange(playItems);
    }

    /**
     * 编排位广告的参数是, 播放下一首时, 使用上一首的编排位信息.
     *
     * @param position
     * @param playItem
     */
    @Override
    protected void addPlayItemParameter(int position, PlayItem playItem) {
        PlayerLogUtil.log(getClass().getSimpleName(),"addPlayItemParameter","position = " + position);
        if (position <= 0) {
            return;
        }
        int playlistSize = mPlayItemArrayList.size();
        int index = position - 1;
        if (playlistSize <= index) {
            return;
        }

        PlayItem tempItem = mPlayItemArrayList.get(index);
        if (tempItem != null) {
            int adZoneId = ((RadioPlayItem) tempItem).getRadioInfoData().getAdZoneId();
            int adZoneType = ((RadioPlayItem) tempItem).getRadioInfoData().getAdZoneChooseType();
            PlayerLogUtil.log(getClass().getSimpleName(),"addPlayItemParameter"," adZoneId = " + adZoneId + ", type = " + adZoneType);
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_ID, String.valueOf(adZoneId));
            playItem.addMapCacheData(PlayItemConstants.ITEM_KEY_AD_ZONE_TYPE, String.valueOf(adZoneType));
        }
    }

    @Override
    public void release() {
        super.release();
        if (mSongPlayItemArrayList != null) {
            mSongPlayItemArrayList.clear();
            isPlaySongList = false;
            mSongListPosition = -1;
        }
    }

}
