package com.kaolafm.opensdk.player.logic.util;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlayerConstants {

    public static final String LOG_TAG = "player_log_tag";

    public static final String LOG_PROGRESS_TAG = "player_log_progress_tag";


    public static final int PAGE_NUMBER_10 = 10;

    /**
     * 有页面信息
     */
    public static final int HAVE_PAGE_DATA = 1;


    /**
     * 播放历史时要减去5秒
     */
    public static final int PLAY_MAGIC_TIME = 5 * 1000;

    /**
     * 无效类型
     */
    public static final int RESOURCES_TYPE_INVALID = -1;
    /**
     * 专辑
     */
    public static final int RESOURCES_TYPE_ALBUM = 0;
    /**
     * 单曲
     */
    public final static int RESOURCES_TYPE_AUDIO = 1;
    /**
     * 电台
     */
    public static final int RESOURCES_TYPE_RADIO = 3;

    /**
     * 直播
     */
    public static final int RESOURCES_TYPE_LIVING = 5;

    /**
     * 一键收听电台类型
     */
    public static final int RESOURCES_TYPE_ONE_KEY_LISTENER_TYPE = 4;
    /**
     * 一键收听-已购
     */
    public static final int RESOURCES_TYPE_PURCHASE_ONE_KEY_LISTENER_TYPE = 6;
    /**
     * 在线广播
     */
    public static final int RESOURCES_TYPE_BROADCAST = 11;

    public static final int RESOURCES_TYPE_TEMP_TASK = 60;

    public static final int RESOURCES_TYPE_LIVE_STREAM = 61;
    /**
     * QQ音乐
     */
    public static final int RESOURCES_TYPE_QQ_MUSIC = 100;


    public static final int RESOURCES_TYPE_MIN_SIZE = 1000;


    public static final int TYPE_PLAYER_IDLE = 1;
    public static final int TYPE_PLAYER_PREPARING = 2;
    public static final int TYPE_PLAYER_PREPARING_COMPLETE = 3;
    public static final int TYPE_PLAYER_PLAYING = 4;
    public static final int TYPE_PLAYER_PROGRESS = 5;
    public static final int TYPE_PLAYER_PAUSED = 6;
    public static final int TYPE_SEEK_START = 7;
    public static final int TYPE_SEEK_COMPLETE = 8;
    public static final int TYPE_BUFFERING_START = 9;
    public static final int TYPE_BUFFERING_END = 10;
    public static final int TYPE_PLAYER_END = 11;
    public static final int TYPE_PLAYER_DOWNLOAD_PROGRESS = 12;
    public static final int TYPE_PLAYER_FAILED = 404;

    /**
     * 临时任务类型-报时
     */
    public static final int TEMP_TASK_TYPE_CLOCK = 1;

    /**
     * 临时任务类型-提示音
     */
    public static final int TEMP_TASK_TYPE_HINT = 2;

    /**
     * 默认状态
     */
    public static final int BROADCAST_STATUS_DEFAULT = 0;
    /**
     * 直播中
     */
    public static final int BROADCAST_STATUS_LIVING = 1;
    /**
     * 可回放
     */
    public static final int BROADCAST_STATUS_PLAYBACK = 2;
    /**
     * 未开播
     */
    public static final int BROADCAST_STATUS_NOT_ON_AIR = 3;


    /**
     * 正序
     */
    public static final int SORT_ACS = 1;

    /**
     * 倒序
     */
    public static final int SORT_DESC = -1;

    /**
     * 最大音量
     */
    public static final float LEFT_VOLUME_MAX = 1.0F;
    public static final float RIGHT_VOLUME_MAX = 1.0F;

    /**
     * 混音最小音量
     */
    public static final float LEFT_VOLUME_MIN = 0.3F;
    public static final float RIGHT_VOLUME_MIN = 0.3F;

    /**
     * 台宣类型
     */
    public static final int CTG_TYPE_TX = 131;

    /**
     * 来源于历史记录
     */
    public static final int DATA_SRC_HISTORY = 1;

    public static final class ToneQuality {
        /**
         * 低音质
         */
        public static final int LOW_TONE_QUALITY = 0X01;

        /**
         * 中音质
         */
        public static final int MIDDLE_TONE_QUALITY = 0X02;

        /**
         * 高音质
         */
        public static final int HIGH_TONE_QUALITY = 0X03;

        /**
         * 超高音质
         */
        public static final int HIGHER_TONE_QUALITY = 0X04;

    }

    /**
     * 专辑已上线
     */
    public static final int ALBUM_ONLINE = 1;

    /**
     * 专辑下线错误
     */
    public static final int ERROR_CODE_ALBUM_OFFLINE = 10001;

    /**
     * 是最后一首
     */
    public static final int ERROR_CODE_IS_LAST_ONE =10002;

    /**
     * 节目开始时间大于系统时间(还没到节目播放的时间)
     */
    public static final int ERROR_PROGRAM_TIME_EXCEED = 10050;

    /**
     * 直播回放URL暂时还未生成(国家台回放,需要总台收录之后才能生成)
     */
    public static final int ERROR_CODE_BACK_PLAY_URL_NOT_GENERATE = 10071;




}

