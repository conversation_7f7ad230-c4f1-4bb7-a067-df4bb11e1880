/*
 * Copyright (C) 2006 The Android Open Source Project
 * Copyright (C) 2013 <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package tv.danmaku.ijk.media.player;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.Surface;

import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;

import java.io.IOException;
import java.lang.ref.WeakReference;

import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;
import tv.danmaku.ijk.media.player.annotations.AccessedByNative;
import tv.danmaku.ijk.media.player.annotations.CalledByNative;
import tv.danmaku.ijk.media.player.option.AvFormatOption;

/**
 * <AUTHOR>
 * <p/>
 * Java wrapper of ffplay.
 */
public final class IjkMediaPlayer implements IjkMediaPlayerConstants {
    private final static String TAG = IjkMediaPlayer.class.getName();

    private ijkPlayerCallBack mijkPlayerCallBack;

    @AccessedByNative
    private long mNativeMediaPlayer;

    @AccessedByNative
    private int mNativeSurfaceTexture;

    @AccessedByNative
    private int mListenerContext;

    @AccessedByNative
    private long mNativeMediaDataSource;
    @AccessedByNative
    private long mNativeAndroidIO;

    private static IjkLibLoader sLocalLibLoader = libName -> System.loadLibrary(libName);

    private static volatile boolean mIsLibLoaded = false;

    public static void loadLibrariesOnce(IjkLibLoader libLoader) {
        synchronized (IjkMediaPlayer.class) {
            if (!mIsLibLoaded) {
                try {
                    libLoader.loadLibrary("kaolafmffmpeg");
                    libLoader.loadLibrary("kaolafmsdl");
                    libLoader.loadLibrary("kaolafmplayer");
                    libLoader.loadLibrary("kaolafmsoundtouch");
                    mIsLibLoaded = true;
                } catch (UnsatisfiedLinkError ule) {
                    ule.printStackTrace();
                } catch (Throwable t) {
                    t.printStackTrace();
                }
            }
        }
    }

    private static volatile boolean mIsNativeInitialized = false;

    private static void initNativeOnce() {
        synchronized (IjkMediaPlayer.class) {
            if (!mIsNativeInitialized) {
                native_init();
                mIsNativeInitialized = true;
            }
        }
    }

    public IjkMediaPlayer() {
        this(sLocalLibLoader);
    }


    /**
     * do not loadLibaray
     *
     * @param libLoader
     */
    public IjkMediaPlayer(IjkLibLoader libLoader) {
        initPlayer(libLoader);
    }

    @SuppressLint("CheckResult")
    private void initPlayer(final IjkLibLoader libLoader) {
        Single.fromCallable(() -> {
            loadLibrariesOnce(libLoader);
            return 1;
        }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(Schedulers.io())
                .subscribe(integer -> {
                    initNativeOnce();
                    native_setup(new WeakReference<>(IjkMediaPlayer.this));
                    // _setLogInValid();
                    if (mijkPlayerCallBack != null) {
                        mijkPlayerCallBack.message(IjkMediaPlayerConstants.MEDIA_IJK_SO_INIT_SUCCESS, 0, 0, 0);
                    }
                });
    }

    public void setLogInValid() {
        if (!mIsNativeInitialized) {
            return;
        }
        _setLogInValid();
    }

    public void setDataSource(String path) throws Exception {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "setDataSource", "setDataSource: " + path);
        _setDataSource(path, null, null);
    }

    public void prepare(int needSeek, int streamTypeChannel) throws IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "prepare", "needSeek = " + needSeek);
        _prepareAsync(needSeek, streamTypeChannel);
    }

    public void seekAtStart(long msec) {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "seekAtStart", "msec =  " + msec);
        _setSeekAtStart(msec);
    }

    public void setDuration(long urlDuration, long totalDuration) throws IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "setDuration", "duration =  " + urlDuration);
        _setDuration((int) urlDuration, (int) totalDuration);
    }

    public void play() throws IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "play", "play");
        _start();
    }

    public void stop() throws IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "stop", "stop");
        _stop();
    }


    public void pause() throws IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "pause", "pause");
        _pause();
    }


    public void seek(long msec) throws Exception {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "seek", "msec = " + msec);
        seekTo(msec);
    }

    public void release() {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "release", "release");

        _release();
    }


    public void reset() throws Exception {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "reset", "reset ");

        _stop();
        _reset();
    }


    public String getDnsAddress() {
        if (!mIsNativeInitialized) {
            return null;
        }
        return _getDnsAdress();
    }


    public void setAutoPlayOnPrepared(boolean enabled) {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "setAutoPlayOnPrepared", "enabled= " + enabled);
        _setAutoPlayOnPrepared(enabled);
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "setMediaVolume", "setMediaVolume ="+ leftVolume );

        setVolume(leftVolume, rightVolume);
    }

    /**
     * 设置IJKPlayer 音量均衡
     *
     * @param active 1 enable loudness normalization  0 disable loudness normalization
     */
    public void setLoudnessNormalization(int active) {
        try {
            _setLoudnessNormalization(active);
        } catch (IllegalStateException ill) {
            ill.printStackTrace();
        }
    }

    public void setAvOption(AvFormatOption option) {
        setAvFormatOption(option.getName(), option.getValue());
    }

    public void setAvFormatOption(String name, String value) {
        _setAvFormatOption(name, value);
    }

    public void setAvCodecOption(String name, String value) {
        _setAvCodecOption(name, value);
    }

    public void setSwScaleOption(String name, String value) {
        _setSwScaleOption(name, value);
    }

    /**
     * @param chromaFourCC AvFourCC.SDL_FCC_RV16 AvFourCC.SDL_FCC_RV32
     *                     AvFourCC.SDL_FCC_YV12
     */
    public void setOverlayFormat(int chromaFourCC) {
        _setOverlayFormat(chromaFourCC);
    }

    /**
     * @param frameDrop =0 do not drop any frame <0 drop as many frames as possible >0
     *                  display 1 frame per `frameDrop` continuous dropped frames,
     */
    public void setFrameDrop(int frameDrop) {
        _setFrameDrop(frameDrop);
    }

    public void setMediaCodecEnabled(boolean enabled) {
        _setMediaCodecEnabled(enabled);
    }

    public void setOpenSLESEnabled(boolean enabled) {
        _setOpenSLESEnabled(enabled);
    }

    public void setAudioFadeEnabled(boolean enabled) {
        _setAudioFadeEnable(enabled ? 1 : 0);
    }

    /**
     * 设置音频淡入时长
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    public void setAudioFadeInDuration(int type, long msec) {
        _setAudioFadeInDuration(type, msec);
    }

    /**
     * 设置音频淡出时长
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    public void setAudioFadeOutDuration(int type, long msec) {
        _setAudioFadeOutDuration(type, msec);
    }

    public void setDnsAddress(String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        _setDnsAddress(values);
    }

    public void clearDnsAddress() throws IOException, IllegalArgumentException, SecurityException, IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        _clearDnsAddress();
    }

    /**
     * 2016.10.21
     * 清除代理方法
     */
    public void clearProxyAddress() {
        if (!mIsNativeInitialized) {
            return;
        }
        _clearProxyAdress();
    }

    /**
     * 2016.10.21
     * 设置代理方法
     */
    public void setProxyAddress(String values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        _setProxyAddress(values);
    }

    /**
     * 2021.1.5
     * 设置清除dns缓存方法
     */
    public void clearDnsCache(String values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException {
        if (!mIsNativeInitialized) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "clearDnsCache", "values ="+ values );
        _clearDnsCache(values);
    }


    public Bundle getMediaMeta() {
        return _getMediaMeta();
    }

    public String getVideoCodecInfo() {
        return _getVideoCodecInfo();
    }

    public String getAudioCodecInfo() {
        return _getAudioCodecInfo();
    }

    protected void finalize() {
        native_finalize();
    }

    public void setIjkPlayerCallBack(ijkPlayerCallBack callBack) {
        this.mijkPlayerCallBack = callBack;
    }

    public ijkPlayerCallBack getIjkPlayerCallBack() {
        return mijkPlayerCallBack;
    }

    /**
     * 本地方法调用上层方法.....
     */

    @CalledByNative
    private static void postDataFromNative(Object weakThiz, byte buffer[], int length) {
    }

    @CalledByNative
    private static void postEventFromNative(Object weakThiz, int what,
                                            int arg1, int arg2, Object obj) {
        if (weakThiz == null)
            return;

        @SuppressWarnings("rawtypes")
        IjkMediaPlayer mp = (IjkMediaPlayer) ((WeakReference) weakThiz).get();
        if (mp == null) {
            return;
        }

        if (what == MEDIA_INFO && arg1 == MEDIA_INFO_STARTED_AS_NEXT) {
            try {
                mp.play();
            } catch (IllegalStateException ill) {
                ill.printStackTrace();
            }
        }
        if (mp.getIjkPlayerCallBack() != null) {
            mp.getIjkPlayerCallBack().message(what, arg1, arg2, obj);
        }
    }

    @CalledByNative
    private static String onSelectCodec(Object weakThiz, String mimeType,
                                        int profile, int level) {
        return null;
    }


    @CalledByNative
    private static String onControlResolveSegmentUrl(Object weakThiz,
                                                     int segment) {
        return null;
    }

    @CalledByNative
    private static String onControlResolveSegmentOfflineMrl(Object weakThiz,
                                                            int segment) {
        return null;
    }

    @CalledByNative
    private static int onControlResolveSegmentDuration(Object weakThiz, int segment) {
        return 0;
    }

    @CalledByNative
    private static int onControlResolveSegmentCount(Object weakThiz) {
        return 0;
    }

    public interface ijkPlayerCallBack {
        void message(int what, int arg1, int arg2, Object obj);
    }


    /**
     * 本地方法.....
     */

    /**
     * @throws IOException
     */
    private native void _clearDnsAddress() throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    /**
     * 1,片花控制有效，0，片花控制无效
     *
     * @param active 是否有片花(广告)
     */
    public native void _setTitileActive(int active) throws IllegalStateException;

    private native long _getAudioFormat();

    private native long _getAudioSampleRate();

    private native long _getAudioChannels();

    private native void _setDataCallBack(long flag) throws IOException,
            IllegalArgumentException, SecurityException, IllegalStateException;

    /*
     * Update the IjkMediaPlayer SurfaceTexture. Call after setting a new
     * display surface.
     */
    private native void _setVideoSurface(Surface surface);

    /**
     * 1 enable loudness normalization  0 disable loudness normalization
     *
     * @param active disable or enable loudness normalization
     */
    private native void _setLoudnessNormalization(int active) throws IllegalStateException;

    private static native final String _getColorFormatName(
            int mediaCodecColorFormat);

    private static native final void native_init();

    private native final void native_setup(Object IjkMediaPlayer_this);

    private native final void native_finalize();

    private native final void native_message_loop(Object IjkMediaPlayer_this);

    private native void _setDnsAddress(String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    /**
     * 2016.07.22新加方法
     *
     * @return
     */
    private native String _getDnsAdress();

    /**
     * 2016.10.21
     * 清除代理方法
     */
    private native void _clearProxyAdress();

    /**
     * 2016.10.21
     * 设置代理方法
     */
    private native void _setProxyAddress(String values);

    /**
     * 2021.1.5
     * 设置清除dns缓存方法
     */
    private native void _clearDnsCache(String values);


    /**
     * @param urlduration 参数单位是以毫秒为精确度的时长
     */
    public native void _setDuration(int urlduration, int urlduraion_all) throws IllegalStateException;

    private native void _start() throws IllegalStateException;

    private native void _setDataSource(String path, String[] keys,
                                       String[] values) throws IOException, IllegalArgumentException,
            SecurityException, IllegalStateException;

    private native void _stop() throws IllegalStateException;

    private native void _setAvFormatOption(String name, String value);

    private native void _setAvCodecOption(String name, String value);

    private native void _setSwScaleOption(String name, String value);

    private native void _setOverlayFormat(int chromaFourCC);

    private native void _setFrameDrop(int frameDrop);

    private native void _setMediaCodecEnabled(boolean enabled);

    private native void _setOpenSLESEnabled(boolean enabled);

    private native Bundle _getMediaMeta();

    private native String _getVideoCodecInfo();

    private native String _getAudioCodecInfo();

    private native void _pause() throws IllegalStateException;

    public native void _prepareAsync(int needSeek, int stream_type_channel) throws IllegalStateException;

    public native void seekTo(long msec) throws IllegalStateException;

    public native boolean isPlaying();

    public native long getCurrentPosition();

    public native long getDuration();

    private native void _release();

    private native void _reset();

    public native void setVolume(float leftVolume, float rightVolume);

    /**
     * 设置播放速率的接口
     *
     * @param rate
     */
    public native void _setPlaybackRate(float rate);

    /**
     * 获取播放速率的接口
     */
    public native float _getPlaybackRate();

    /**
     * 起播前Seek（设置播放位置，默认从0开始播放）
     *
     * @param msec: 起播位置，单位毫秒
     */
    public native void _setSeekAtStart(long msec);

    /**
     * 自动播放（准备完后自动播放，默认自动播放）
     *
     * @param enable: True  功能开启
     *                False 功能关闭
     */
    public native void _setAutoPlayOnPrepared(boolean enable);

    /**
     * 控制IJK动态库中log信息是否可以输出如果调用则不可以输出，默认情况为输出
     */
    private native void _setLogInValid();

    /**
     * // 设置日志上报（使 FFmpeg 用 IJKPlayer 的日志函数进行日志输出，默认使用 brief 方式）
     *
     * @param useReport: 0 使用 brief  的方式（） 1 使用 report 的方式（使用 FFmpeg 定义的格式）
     */
    public native void _setLogReport(int useReport);

    /**
     * 设置日志等级（分模块）
     * 默认 LOG_MOUDLE_IJK_MEDIA 的日志等级为 IJK_LOG_DEFAULT
     * 默认 LOG_MOUDLE_IJK_LIBAV 的日志等级为 IJK_LOG_INFO
     *
     * @param moudle: LOG_MOUDLE_IJK_MEDIA、LOG_MOUDLE_IJK_LIBAV
     * @param level   : IJK_LOG_XXX
     */
    public native void _setLogLevel(int moudle, int level);

    @CalledByNative
    private static boolean onNativeInvoke(Object weakThiz, int what, Bundle args) {
        return true;
    }

    /**
     * ijk sdk 版本
     *
     * @return
     */
    private native String _getLibVersion();

    /**
     * 是否启用音频淡入淡出功能
     *
     * @param enable: 0-不启用（默认）、1-开启
     */
    public native void _setAudioFadeEnable(int enable);

    /**
     * 设置音频淡入时长
     * @param type 淡入类型（或类型的组合）0x11, 0x12, 0x13：播放开始淡入、Seek起播淡入、Pause起播淡入
     * @param msec 淡入时长，单位为毫秒（ms）
     */
    public native void _setAudioFadeInDuration(int type, long msec);

    /**
     * 设置音频淡出时长
     * @param type 淡出类型（或类型的组合）0x21, 0x22, 0x23：播放结束淡出、Stop起播淡出、Pause停播淡出
     * @param msec 淡出时长，单位为毫秒（ms）
     */
    public native void _setAudioFadeOutDuration(int type, long msec);
}
