import com.kaolafm.gradle.plugin.model.MavenConfig

apply plugin: "build-jar"
def config = rootProject.ext.config
def VERSION_NAME = rootProject.ext.android.versionName
def VERSION_CODE = rootProject.ext.android.versionCode
def lastedReleaseMavenVersionName = rootProject.ext.config["repository"] == "pub"
        ? rootProject.ext.android.versionName
        : rootProject.ext.android.versionName + "-SNAPSHOT"
def lastedDebugMavenVersionName = lastedReleaseMavenVersionName+config["domain_type"]

upload {
    sdkFlavors {
        common {
            includeSourceJava = [
                    'com/kaolafm/opensdk/api/**/model/**/*.java',
                    'com/kaolafm/opensdk/api/broadcast/BroadcastDetails.java',
                    'com/kaolafm/opensdk/api/broadcast/ProgramDetails.java',
                    'com/kaolafm/opensdk/api/scene/SceneInfo.java',
                    'com/kaolafm/opensdk/api/scene/Scene.java',
                    'com/kaolafm/opensdk/api/scene/AccScene.java',
                    'com/kaolafm/opensdk/api/scene/SpeedScene.java',
                    'com/kaolafm/opensdk/api/search/SearchProgramBean.java',
                    'com/kaolafm/opensdk/api/search/VoiceSearchResult.java',
                    'com/kaolafm/opensdk/api/subscribe/SubscribeInfo.java',
                    'com/kaolafm/opensdk/api/subscribe/SubscribeStatus.java',
                    'com/kaolafm/opensdk/api/**/*Request.java',
                    'com/kaolafm/opensdk/api/BasePageResult.java',
                    'com/kaolafm/opensdk/utils/operation/OperationAssister.java',
                    'com/kaolafm/opensdk/ResType.java']
            excludeSourceJava = ['com/kaolafm/opensdk/api/init/*.java',
                                 'com/kaolafm/opensdk/api/BaseRequest.java',
                                 'com/kaolafm/**/*Constant.java']
            proguardConfigFile = ["proguard-sdk-rules.pro"]
            includePackage = ["com/kaolafm/opensdk", "com/kaolafm/sdk", 'com/kaolafm/base', 'com/kaolafm/report', 'tv/danmaku/ijk/media/player', 'com/kaolafm/ad']
            versionName = VERSION_NAME
            mavenConfig {
                mavenType       MavenConfig.TYPE_MAVEN_NEXUS
                artifactId     'open-sdk'
                groupId        'com.kaolafm'
                libType        'aar'
                libDescription 'K-radio SDK'
                repository      'http://iovnexus.radio.cn/repository/ClientReleases/'
                userName        'ClientDevelopment'
                password        'K29Kcn$ujHbbQE6k'
            }
        }

        def devRepo = readLocalProperties('privete.maven.url')
        dev {
            includeSourcePackage = ['com/kaolafm/ad/api/model/*.java']

            includeSourceJava = [
                    'com/kaolafm/opensdk/api/**/model/**/*.java',
                    'com/kaolafm/opensdk/api/broadcast/BroadcastDetails.java',
                    'com/kaolafm/opensdk/api/broadcast/ProgramDetails.java',
                    'com/kaolafm/opensdk/api/scene/SceneInfo.java',
                    'com/kaolafm/opensdk/api/scene/Scene.java',
                    'com/kaolafm/opensdk/api/scene/AccScene.java',
                    'com/kaolafm/opensdk/api/scene/SpeedScene.java',
                    'com/kaolafm/opensdk/api/search/SearchProgramBean.java',
                    'com/kaolafm/opensdk/api/search/VoiceSearchResult.java',
                    'com/kaolafm/opensdk/api/subscribe/SubscribeInfo.java',
                    'com/kaolafm/opensdk/api/subscribe/SubscribeStatus.java',
                    'com/kaolafm/opensdk/api/**/*Request.java',
                    'com/kaolafm/opensdk/api/BasePageResult.java',
                    'com/kaolafm/opensdk/utils/operation/OperationAssister.java',
                    'com/kaolafm/opensdk/account/token/AccessTokenManager.java',
                    'com/kaolafm/opensdk/OpenSDK.java',
                    'com/kaolafm/opensdk/Options.java',
                    'com/kaolafm/opensdk/ResType.java',
                    'com/kaolafm/ad/AdvertOptions.java',
                    'com/kaolafm/ad/timer/TimedAdvertManager.java',
                    'com/kaolafm/ad/api/AdvertisingRequest.java',
                    'com/kaolafm/ad/expose/AdvertisingManager.java',
                    'com/kaolafm/ad/expose/AdvertisingImager.java',
                    'com/kaolafm/ad/expose/AdvertisingPlayer.java',
                    'com/kaolafm/ad/expose/AdvertisingLifecycleCallback.java',
                    'com/kaolafm/ad/expose/AdvertInterceptor.java']
            excludeSourceJava = ['com/kaolafm/opensdk/api/init/*.java',
                                 'com/kaolafm/opensdk/api/BaseRequest.java',
                                 'com/kaolafm/opensdk/api/AbstractRequest.java']
            includePackage = ['com/kaolafm/opensdk', 'com/kaolafm/base', 'com/kaolafm/report', 'com/kaolafm/ad', 'tv/danmaku/ijk/media/player']
            excludeClass = []
            proguardConfigFile = ["proguard-dev-rules.pro"]
            versionName = "********.benteng"
//            versionName = "********.benteng.local.01"
            includeOldSDK = false
            mavenConfig {
                mavenType       MavenConfig.TYPE_MAVEN_NEXUS
                artifactId      'open-sdk'
                groupId         'com.kaolafm'
                libType         'aar'
                libDescription  'K-radio SDK'
                repository      'http://iovnexus.radio.cn/repository/ClientReleases/'
                userName        'ClientDevelopment'
                password        'K29Kcn$ujHbbQE6k'
            }
        }
        jarForOld {
            includePackage = ["com/kaolafm/opensdk"]
            versionName = "*******"
        }
        log {
            includePackage = ['com/kaolafm/opensdk/log']
            versionName = "1.0.0"
            outputFileName = "logging"
        }
    }

}
def readLocalProperties(String name) {
    def properties = new Properties()
    File file = project.rootProject.file('local.properties')
    if (file.exists()) {
        def inputStream = file.newDataInputStream()
        properties.load(inputStream)
        def result = properties.getProperty(name)
        if (result == null || result == "") {
            result = "file://${project.projectDir.absolutePath}/maven"
        }
        return result
    }
}