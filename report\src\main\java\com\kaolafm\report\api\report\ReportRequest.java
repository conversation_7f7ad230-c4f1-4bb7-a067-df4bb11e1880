package com.kaolafm.report.api.report;


import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.util.ReportConstants;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import retrofit2.Response;

/**
 * <AUTHOR> on 2019/3/1.
 */

public class ReportRequest extends BaseRequest {
    private ReportApiService reportApiService;

    public ReportRequest() {
        mUrlManager.putDomain(ReportConstants.REPORT_DOMAIN_NAME, ReportConstants.REPORT_BASE_URL);
        reportApiService = mRepositoryManager.obtainRetrofitService(ReportApiService.class);
    }


    public boolean postReport(String json) {
        Logging.i(ReportConstants.REPORT_TAG, "发送数据 = " + json);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), json);
        Response<String> baseResult = mRepositoryManager
                .doHttpDealSyncResponse(reportApiService.report(requestBody));

        if (baseResult != null) {
            Logging.i(ReportConstants.REPORT_TAG, "发送完数据: isSuccessful = " + baseResult.isSuccessful() + " message = " + baseResult.message() + " code = " + baseResult.code() + " body = " + baseResult.body());
        } else {
            Logging.i(ReportConstants.REPORT_TAG, "发送数据 完成 result 为空");
        }

        return baseResult != null && baseResult.isSuccessful();
    }
}
