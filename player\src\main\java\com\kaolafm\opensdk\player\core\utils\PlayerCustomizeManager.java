package com.kaolafm.opensdk.player.core.utils;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.opensdk.player.core.listener.ICheckCanPlayInter;
import com.kaolafm.opensdk.player.core.listener.KLAudioFocusOperationListener;
import com.kaolafm.opensdk.player.core.listener.KLAudioStateChangedByAudioFocusListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusRequestStreamTypeListener;
import com.kaolafm.opensdk.player.core.listener.OnHandleAudioFocusListener;
import com.kaolafm.opensdk.player.core.listener.OnPlayEnd;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.listener.OnPlayStart;
import com.kaolafm.opensdk.player.core.listener.OnPlayStreamTypeListener;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.UIThreadUtil;

import java.util.concurrent.CopyOnWriteArrayList;

import static android.media.AudioManager.STREAM_MUSIC;

/**
 * 播放器自定义管理类
 * 支持自定义 设置音频流
 * 支持自定义 阻断音频焦点变化带来的播放动作等
 * 支持自定义 阻断播放操作
 */
public class PlayerCustomizeManager {
    private volatile static PlayerCustomizeManager playerCustomizeManager;

    private OnPlayStreamTypeListener mPlayStreamTypeListener;
    private OnAudioFocusRequestStreamTypeListener mAudioFocusRequestStreamTypeListener;
    private OnHandleAudioFocusListener mHandleAudioFocusListener;
    private OnPlayLogicListener mPlayLogicListener;
    private ICheckCanPlayInter mCheckCanPlayInter;
    private OnPlayStart mOnPlayStart;
    private OnPlayEnd mOnPlayEnd;
    KLAudioFocusOperationListener mKLAudioFocusOperationListener;

    private boolean isDisposePlayOnce = false;
    /**
     * 是否需要请求音频焦点,
     */
    private boolean isNeedRequestAudioFocus = true;

    private CopyOnWriteArrayList<KLAudioStateChangedByAudioFocusListener> mKLAudioStateChangedByAudioFocusListeners;


    private PlayerCustomizeManager() {

    }

    public static PlayerCustomizeManager getInstance() {
        if (playerCustomizeManager == null) {
            synchronized (PlayerCustomizeManager.class) {
                if (playerCustomizeManager == null) {
                    playerCustomizeManager = new PlayerCustomizeManager();
                }
            }
        }
        return playerCustomizeManager;
    }

    public OnPlayStreamTypeListener getPlayStreamTypeListener() {
        return mPlayStreamTypeListener;
    }

    public void setPlayStreamTypeListener(OnPlayStreamTypeListener playStreamTypeListener) {
        this.mPlayStreamTypeListener = playStreamTypeListener;
    }

    public OnHandleAudioFocusListener getHandleAudioFocusListener() {
        return mHandleAudioFocusListener;
    }

    public void setOnHandleAudioFocusListener(OnHandleAudioFocusListener handleAudioFocusListener) {
        mHandleAudioFocusListener = handleAudioFocusListener;
    }

    public OnPlayLogicListener getPlayLogicListener() {
        return mPlayLogicListener;
    }

    public void setPlayLogicListener(OnPlayLogicListener playLogicListener) {
        this.mPlayLogicListener = playLogicListener;
    }

    public OnAudioFocusRequestStreamTypeListener getAudioFocusRequestStreamTypeListener() {
        return mAudioFocusRequestStreamTypeListener;
    }

    public void setAudioFocusRequestStreamTypeListener(OnAudioFocusRequestStreamTypeListener audioFocusRequestStreamTypeListener) {
        this.mAudioFocusRequestStreamTypeListener = audioFocusRequestStreamTypeListener;
    }

    public ICheckCanPlayInter getCheckCanPlayInter() {
        return mCheckCanPlayInter;
    }

    public OnPlayStart getOnPlayStart() {
        return mOnPlayStart;
    }

    public void setOnPlayStart(OnPlayStart onPlayStart) {
        this.mOnPlayStart = onPlayStart;
    }

    public OnPlayEnd getOnPlayEnd() {
        return mOnPlayEnd;
    }

    public void setOnPlayEnd(OnPlayEnd onPlayEnd) {
        this.mOnPlayEnd = onPlayEnd;
    }

    /**
     * 注册一个处理SDK流量播放前的check流程
     *
     * @param clazzName
     */
    public void injectKLCheckCanPlayListener(String clazzName) {
        if (this.mCheckCanPlayInter != null) {
            return;
        }
        Object obj = initClazz(clazzName);
        if (obj == null) {
            return;
        }

        this.mCheckCanPlayInter = (ICheckCanPlayInter) obj;
    }

    /**
     * 是否阻断播放功能
     *
     * @return
     */
    public boolean disposePlay() {
        boolean isDispose = mPlayLogicListener != null && mPlayLogicListener.onPlayLogicDispose();
        PlayerLogUtil.log(getClass().getSimpleName(), "disposePlay", "isDispose = " + isDispose);
        return isDispose;
    }

    public boolean isDisposePlayOnce() {
        if(isDisposePlayOnce) {
            Log.i("PlayerCustomizeManager", "isDisposePlayOnce true");
            isDisposePlayOnce = false;
            return true;
        } else {
            Log.i("PlayerCustomizeManager", "isDisposePlayOnce false");
            return false;
        }

    }

    public void setDisposePlayOnce() {
        Log.i("PlayerCustomizeManager", "setDisposePlayOnce");
        isDisposePlayOnce = true;
    }

    /**
     * 获取播放stream
     *
     * @return
     */
    public int getStreamChannel() {
        int streamTypeChannel = STREAM_MUSIC;
        if (mPlayStreamTypeListener != null) {
            streamTypeChannel = mPlayStreamTypeListener.getMusicStreamType();
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "getStreamChannel", "streamTypeChannel:" + streamTypeChannel);
        return streamTypeChannel;
    }

    /**
     * 获取 请求音频焦点stream
     *
     * @return
     */
    public int getRequestAudioFocusStreamType() {
        int streamTypeChannel = STREAM_MUSIC;
        if (mAudioFocusRequestStreamTypeListener != null) {
            streamTypeChannel = mAudioFocusRequestStreamTypeListener.getAudioFocusStreamType();
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "getRequestAudioFocusStreamType", "streamTypeChannel =" + streamTypeChannel);
        return streamTypeChannel;
    }

    /**
     * 是否阻断duck音频焦点状态
     *
     * @return
     */
    public boolean disposeAudioFocusChangeDuck() {
        boolean isDispose = false;
        if (mHandleAudioFocusListener != null) {
            mHandleAudioFocusListener.onAudioFocusDuck();
            isDispose = true;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "disposeAudioFocusChangeDuck", "isDispose = " + isDispose);
        return isDispose;
    }

    private Object initClazz(String clazzName) {
        Object obj = null;
        try {
            Class clazz = Class.forName(clazzName);
            if (clazz == null) {
                return null;
            }
            obj = clazz.newInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }

    public boolean isNeedRequestAudioFocus() {
        return isNeedRequestAudioFocus;
    }

    public void setNeedRequestAudioFocus(boolean needRequestAudioFocus) {
        isNeedRequestAudioFocus = needRequestAudioFocus;
    }

    public void setKLAudioFocusOperationListener(KLAudioFocusOperationListener klAudioFocusOperationListener) {
        this.mKLAudioFocusOperationListener = klAudioFocusOperationListener;
    }

    public boolean beforeRequestAudioFocus(Object... args) {
        if (mKLAudioFocusOperationListener != null) {
            boolean isRequest = mKLAudioFocusOperationListener.beforeRequestAudioFocus(args);
            PlayerLogUtil.log(getClass().getSimpleName(), "beforeRequestAudioFocus", "isRequest = " + isRequest);
            return isRequest;
        }
        return false;
    }

    public void notifyPlayerStateChangedByAudioFocus(final boolean isPlaying) {
        UIThreadUtil.runUIThread(() -> notifyPlayerStateChangedByAudioFocusInner(isPlaying));
    }

    private void notifyPlayerStateChangedByAudioFocusInner(boolean isPlaying) {
        if (mKLAudioStateChangedByAudioFocusListeners == null) {
            return;
        }
        for (int i = 0, size = mKLAudioStateChangedByAudioFocusListeners.size(); i < size; i++) {
            KLAudioStateChangedByAudioFocusListener klAudioStateChangedByAudioFocusListener = mKLAudioStateChangedByAudioFocusListeners.get(i);
            if (isPlaying) {
                klAudioStateChangedByAudioFocusListener.onAudioStatePlayingByGainAudioFocus();
            } else {
                klAudioStateChangedByAudioFocusListener.onAudioStatePausedByLossAudioFocus();
            }
        }
    }

    public void registerAudioStateChangedByAudioFocusListener(KLAudioStateChangedByAudioFocusListener klAudioStateChangedByAudioFocusListener) {
        if (klAudioStateChangedByAudioFocusListener == null) {
            return;
        }
        if (mKLAudioStateChangedByAudioFocusListeners == null) {
            mKLAudioStateChangedByAudioFocusListeners = new CopyOnWriteArrayList<>();
        }
        if (mKLAudioStateChangedByAudioFocusListeners.contains(klAudioStateChangedByAudioFocusListener)) {
            return;
        }
        mKLAudioStateChangedByAudioFocusListeners.add(klAudioStateChangedByAudioFocusListener);
    }

    public void unregisterAudioStateChangedByAudioFocusListener(KLAudioStateChangedByAudioFocusListener klAudioStateChangedByAudioFocusListener) {
        if (mKLAudioStateChangedByAudioFocusListeners == null || klAudioStateChangedByAudioFocusListener == null) {
            return;
        }

        if (mKLAudioStateChangedByAudioFocusListeners.contains(klAudioStateChangedByAudioFocusListener)) {
            mKLAudioStateChangedByAudioFocusListeners.remove(klAudioStateChangedByAudioFocusListener);
        }
    }

}
