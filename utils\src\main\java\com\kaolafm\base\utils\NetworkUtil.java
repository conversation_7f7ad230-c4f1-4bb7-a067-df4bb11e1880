package com.kaolafm.base.utils;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.NetworkInfo.State;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.telephony.TelephonyManager;
import android.util.Log;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.util.Enumeration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static android.content.Context.CONNECTIVITY_SERVICE;


public class NetworkUtil {
    private static final String TAG = NetworkUtil.class.getSimpleName();
    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();
    public static final int NETWORK_TYPE_UNKNOW = -1;
    public static final int NETWORK_TYPE_NO_WORK = 0;
    public static final int NETWORK_TYPE_WIFI = 1;
    public static final int NETWORK_TYPE_2G = 2;
    public static final int NETWORK_TYPE_3G = 3;
    public static final int NETWORK_TYPE_4G = 4;


    /**
     * 是否去需要去校验当前网络是否可用true为是，false为否
     */
    private static boolean bCheckedNetInfo = true;

    /**
     * 获取当前设备的Mac地址。如果获取失败，返回空字符串。
     *
     * @return
     */
    public static String getMacAddr(Context context) {
        try {
            WifiManager wifi = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            WifiInfo info = wifi.getConnectionInfo();
            return info.getMacAddress();
        } catch (Throwable e) {
            return "";
        }
    }

    /**
     * 检测当前网络是否为WAP上网方式
     *
     * @param context
     * @return true为是，false为否
     */
    public static boolean isWAPStatic(Context context) {
        ConnectivityManager connectManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        try {
            NetworkInfo info = connectManager.getActiveNetworkInfo();
            if (info == null || !info.isConnected()) {
                return false;
            }
            if (info.getExtraInfo().contains("wap") || info.getExtraInfo().contains("WAP")) {
                return true;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * Returns whether the network is available
     */
    public static boolean isNetworkAvailable(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
            } else {
                NetworkInfo infos = connectivity.getActiveNetworkInfo();
                if (infos != null && infos.isConnected()) {
                    return true;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 判断当前是否为漫游连接
     */
    public static boolean isNetworkRoaming(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
            } else {
                NetworkInfo info = connectivity.getActiveNetworkInfo();
                if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE) {
                    TelephonyManager telManager = (TelephonyManager) context
                            .getSystemService(Context.TELEPHONY_SERVICE);
                    if (telManager != null && telManager.isNetworkRoaming()) {
                        return true;
                    } else {
                    }
                } else {
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * wifi连接
     *
     * @param context
     * @return 如果是wifi 并且 也处于连接状态中则返回真
     */
    public static boolean isWifiNetworkAvailable(Context context) {
        try {
            ConnectivityManager connectManager = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo info = connectManager.getActiveNetworkInfo();
            if (info == null || !info.isConnected()) {
                return false;
            }
            if (info.getType() == ConnectivityManager.TYPE_WIFI) {
                return true;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 3G网
     * 3G 类型繁多，如果非2G, 4G ，则归为3G
     *
     * @param context
     * @return 如果是手机上网并且 也处于连接状态中则返回真
     */
    public static boolean isNGNetworkAvailable(Context context) {
        try {
            NetworkInfo info = ((ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE))
                    .getActiveNetworkInfo();
            if (info == null || !info.isConnected()) {
                return false;
            }

            int currentNetworkType = info.getType();
            if (currentNetworkType == ConnectivityManager.TYPE_MOBILE) {
                return true;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 判断当前网络是否已经连接，并且是2G状态.
     *
     * @param context
     * @return true, or false
     */
    public static boolean is2GMobileNetwork(Context context) {
        ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info;
        try {
            info = manager.getActiveNetworkInfo();
            if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE) {
                int currentNetworkType = info.getSubtype();
                if (currentNetworkType == TelephonyManager.NETWORK_TYPE_GPRS
                        || currentNetworkType == TelephonyManager.NETWORK_TYPE_CDMA
                        || currentNetworkType == TelephonyManager.NETWORK_TYPE_EDGE
                        || currentNetworkType == TelephonyManager.NETWORK_TYPE_1xRTT
                        || currentNetworkType == TelephonyManager.NETWORK_TYPE_IDEN) {
                    return true;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 判断当前网络是否已经连接，并且是4G状态. 根据产品的定义，4G为LTE;
     *
     * @param context
     * @return true, or false
     */
    public static boolean is4GMobileNetwork(Context context) {
        ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo info;
        try {
            info = manager.getActiveNetworkInfo();
            if (info != null && info.getType() == ConnectivityManager.TYPE_MOBILE) {
                int currentNetworkType = info.getSubtype();
                if (currentNetworkType == TelephonyManager.NETWORK_TYPE_LTE) {
                    return true;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    public static State getState(Context context) {
        NetworkInfo info = ((ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE))
                .getActiveNetworkInfo();
        if (info == null) {
            return State.UNKNOWN;
        }
        return info.getState();
    }

    /**
     * 获取当前可使用的网络类型。
     *
     * @return 结果可能为：wifi 4g 3g 2g nonet none
     */
    public static String getCurrentAvailableNetworkType(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
                return "none";
            }
            if (!isNetworkAvailable(context)) {
                return "nonet";
            }
            if (isWifiNetworkAvailable(context)) {
                return "wifi";
            }
            if (is2GMobileNetwork(context)) {
                return "2g";
            }
            if (is4GMobileNetwork(context)) {
                return "4g";
            }
            if (isNGNetworkAvailable(context)) {
                return "3g";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "nonet";
    }

    public static int getNetworkIndex(Context context) {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo == null) {
                return 0;
            }
            if (networkInfo.getType() == ConnectivityManager.TYPE_WIFI) {
                return 1;
            } else if ((networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_EDGE
                    || networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_GPRS
                    || networkInfo.getSubtype() == TelephonyManager.NETWORK_TYPE_CDMA)) {
                return 2;
            } else if (networkInfo.getType() == ConnectivityManager.TYPE_MOBILE) {
                return 3;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }


    /**
     * 获取当前的网络状态
     *
     * @param context
     * @return
     */
    public static int getNetwork(Context context) {
        try {
            ConnectivityManager connectivity = (ConnectivityManager) context
                    .getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivity == null) {
                return NETWORK_TYPE_UNKNOW;
            }
            if (!isNetworkAvailable(context)) {
                return NETWORK_TYPE_NO_WORK;
            }
            if (isWifiNetworkAvailable(context)) {
                return NETWORK_TYPE_WIFI;
            }
            if (is2GMobileNetwork(context)) {
                return NETWORK_TYPE_2G;
            }
            if (is4GMobileNetwork(context)) {
                return NETWORK_TYPE_4G;
            }
            if (isNGNetworkAvailable(context)) {
                return NETWORK_TYPE_3G;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return NETWORK_TYPE_UNKNOW;
    }

    public static String getIPAddress(Context context) {
        if (context == null) {
            Log.e(TAG, "context is null, you must init first!");
            return null;
        }
        NetworkInfo info = ((ConnectivityManager) context.getSystemService(CONNECTIVITY_SERVICE)).getActiveNetworkInfo();//获取系统的连接服务
        if (info != null && info.isConnected()) {
            if ((info.getType() == ConnectivityManager.TYPE_MOBILE) || (info.getType() == ConnectivityManager.TYPE_WIFI)) {//当前使用2G/3G/4G网络
                try {
                    for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                        NetworkInterface intf = en.nextElement();
                        for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                            InetAddress inetAddress = enumIpAddr.nextElement();
                            if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                                return inetAddress.getHostAddress();
                            }
                        }
                    }
                } catch (SocketException e) {
                    e.printStackTrace();
                }
            }
        } else { //当前无网络连接,请在设置中打开网络
            return null;
        }
        return null;
    }

    public static void isNetworkAvailableByHttp(String url, PingCallback callback) {
        if (url == null || url.isEmpty()) {
            Log.e(TAG, "URL is null or empty");
            if (callback != null) {
                callback.onResult(false);
            }
            return;
        }

        executorService.execute(() -> {
            HttpURLConnection connection = null;
            try {
                URL requestUrl = new URL(url);
                connection = (HttpURLConnection) requestUrl.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(3000); // 设置连接超时
                connection.setReadTimeout(2000);    // 设置读取超时

                int responseCode = connection.getResponseCode();
                boolean isNetworkAvailable = (responseCode == HttpURLConnection.HTTP_OK);
                Log.i(TAG, "HTTP response code: " + responseCode);

                if (callback != null) {
                    callback.onResult(isNetworkAvailable);
                }
            } catch (IOException e) {
                Log.e(TAG, "HTTP request exception: " + e.getMessage());
                if (callback != null) {
                    callback.onResult(false);
                }
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
            }
        });


    }
}
