package com.kaolafm.report.util;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.ReportBeanBigData;
import com.kaolafm.report.model.ReportTask;

import java.util.LinkedList;

import io.reactivex.Single;

/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportTaskHelperBigData {
    private static ReportTaskHelperBigData reportTaskHelper;
    private boolean isRunning = false;
    LinkedList<ReportTask> reportTaskLinkedList;
    private boolean isClose = false;

    private ReportTaskHelperBigData() {
        reportTaskLinkedList = new LinkedList<>();
        ReportTimerManagerBigData.getInstance().init();
    }

    public static ReportTaskHelperBigData getInstance() {
        if (reportTaskHelper == null) {
            synchronized (ReportTaskHelperBigData.class) {
                if (reportTaskHelper == null) {
                    reportTaskHelper = new ReportTaskHelperBigData();
                }
            }
        }
        return reportTaskHelper;
    }

    public synchronized void insertTask(ReportTask reportTask) {
        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "队列大小 = " + reportTaskLinkedList.size());
        reportTaskLinkedList.add(reportTask);

        if (!isRunning) {
            Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "没有再运行");
            isRunning = true;
            run();
        }
    }

    public void taskDone() {
        run();
    }

    public synchronized ReportTask getTask() {
        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "获取队列大小 = " + reportTaskLinkedList.size());
        if (reportTaskLinkedList.size() <= 0) {
            return null;
        }
        return reportTaskLinkedList.pop();
    }

    public void run() {
        ReportTask reportTask = getTask();
        if (reportTask == null) {
            Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "没有任何任务了. 停止运行");
            isRunning = false;
            return;
        }

        switch (reportTask.getType()) {
            case ReportConstants.TASK_TYPE_INSERT: {
                Single<Long> single = reportTask.getSingleTask();
                if (single != null) {
                    Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "插入数据");
                    single.subscribe(count -> {
                        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "数据库大小 = " + count);
                        if (count >= ReportConstants.READ_DATA_BASE_MAX_COUNT && !isHasSendTask()) {
                            ReportTimerManagerBigData.getInstance().addSendTask();
                        }
                        taskDone();
                    }, throwable -> {
                        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "插入数据库出现异常");
                        taskDone();
                    });
                }
            }
            break;
            case ReportConstants.TASK_TYPE_SEND_DATA: {
                Single<ReportBeanBigData> single = reportTask.getSingleTask();
                if (single != null) {
                    Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送数据");
                    single.subscribe(reportBean -> {
                        if (reportBean != null && !StringUtil.isEmpty(reportBean.getReportJson())) {
                            ReportUploadTaskBigData reportUploadTask = new ReportUploadTaskBigData(reportBean);
                            reportUploadTask.report();
                        } else {
                            Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "读取完数据库, 大小为0");
                            taskDone();
                        }
                    }, throwable -> {
                        Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "读取数据库出现异常");
                        taskDone();
                    });
                }
            }
            break;
            case ReportConstants.TASK_TYPE_DELETE: {
                Single<Long> single = reportTask.getSingleTask();
                if (single != null) {
                    Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "删除数据");
                    single.subscribe(count -> {
                        Logging.i("logx", "读取数据库大小 = " + count);
                        if (count > ReportConstants.READ_DATA_BASE_MAX_COUNT && !isHasSendTask()) {
                            ReportTimerManagerBigData.getInstance().addSendTask();
                        }
                        taskDone();
                    }, throwable -> {
                        taskDone();
                    });
                }
            }
            break;
            default:
                break;
        }
    }

    /**
     * 队列里面是否还有未发送的任务
     *
     * @return
     */
    public boolean isHasSendTask() {
        if (reportTaskLinkedList.size() <= 0) {
            return false;
        }

        for (int i = 0; i < reportTaskLinkedList.size(); i++) {
            ReportTask reportTask = reportTaskLinkedList.get(i);
            if (reportTask == null) {
                continue;
            }
            if (reportTask.getType() == ReportConstants.TASK_TYPE_SEND_DATA) {
                Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "队列里面有未发送的数据");
                return true;
            }
        }

        return false;
    }

    public void release() {
        isClose = true;
        reportTaskLinkedList.clear();
    }

}
