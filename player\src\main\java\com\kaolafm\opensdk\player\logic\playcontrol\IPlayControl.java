package com.kaolafm.opensdk.player.logic.playcontrol;


import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;

/**
 * <AUTHOR> on 2019/3/18.
 */

public interface IPlayControl {

    /**
     * 设置播放状态回调
     *
     * @param iPlayerStateListener
     */
    void setPlayStateListener(BasePlayStateListener iPlayerStateListener);

    /**
     * 设置serviceBind
     *
     * @param mediaPlayerServiceBind
     */
    void setBind(PlayerService.PlayerServiceBinder mediaPlayerServiceBind);

    /**
     * 播放被暂停的音频
     */
    void play();

    /**
     * 暂停正在播放的音频
     */
    void pause();

    /**
     * 停止正在播放的音频
     */
    void stop();

    /**
     * 重置播放器使其回到IDLE状态
     */
    void reset();

    /**
     * 释放播放器资源
     */
    void release();

    /**
     * 快进或快退到播放器的某个时间点
     *
     * @param position
     */
    void seek(int position);

    /**
     * 销毁播放器管理实例
     */
    void destroy();

    /**
     * 临时任务
     *
     * @param url
     */
    void playTempTask(String url);

    /**
     * 开始播放
     */
    void start(int type, PlayItem playItem);

    /**
     * 设置是否支持音量均衡
     *
     * @param isActive
     */
    void setLoudnessNormalization(int isActive);

    /**
     * 是否正在播放
     *
     * @return
     */
    boolean isPlaying();

    /**
     * 请求音频焦点
     */
    boolean requestAudioFocus();

    /**
     * 释放音频焦点
     */
    boolean abandonAudioFocus();

    /**
     * 设置自定义音频焦点控制
     *
     * @param audioFocus
     */
    void setCustomAudioFocus(AAudioFocus audioFocus);

    /**
     * 设置音频焦点listener
     *
     * @param iAudioFocusListener
     */
    void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener);

    /**
     * 获取当前播放时间点
     */
    long getCurrentPosition();

    /**
     * 设置音量
     *
     * @param leftVolume
     * @param rightVolume
     */
    void setMediaVolume(float leftVolume, float rightVolume);

    /**
     * 获取播放状态
     *
     * @return
     */
    int getPlayStatus();

    /**
     * 设置log无效
     */
    void setLogInValid();

    /**
     * 设置要播放的url, 但是不播放
     *
     * @param uri
     * @param position
     */
    void setPlayUrl(String uri, long position, long duration);

    /**
     * 禁用淡入淡出效果
     */
    void disableAudioFade();

    /**
     * 设置HTTP代理
     * @param httpProxy HTTP代理地址
     */
    void setHttpProxy(String httpProxy);

    /**
     * 清除HTTP代理
     */
    void clearHttpProxy();

    /**
     * 清除dns缓存
     */
    void clearDnsCache(boolean clearDnsCache);

    /**
     * 设置淡入淡出效果配置
     * @param audioFadeConfig
     */
    void setAudioFadeConfig(AudioFadeConfig audioFadeConfig);

    /**
     * 异步启动播放是否正在执行
     * @return
     */
    boolean isAsyncStartExecuting();
}
