package com.kaolafm.opensdk.api.operation;

import android.support.annotation.IntDef;

import com.kaolafm.opensdk.ResType;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.MemberNum;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.http.core.HttpCallback;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * 运营网络请求类
 *
 * <AUTHOR> Yan
 * @date 2018/7/30
 */

public class OperationRequest extends BaseRequest {

    private final OperationService mOperationService;

    @Retention(RetentionPolicy.SOURCE)
    @IntDef({ResType.TYPE_ALBUM, ResType.TYPE_BROADCAST, ResType.TYPE_LIVE, ResType.TYPE_RADIO, ResType.TYPE_QQ_MUSIC,ResType.TYPE_NEWS,
            ResType.TYPE_ALL})
    private @interface ContentType {

    }

    public OperationRequest() {
        mOperationService = obtainRetrofitService(OperationService.class);
    }

    /**
     * 获取某一内容类型的整颗分类树
     *
     * @param contentType 必填 内容类型 {@link ResType}
     * @param zone        选填 区域信息
     * @param extInfo     选填 拓展信息
     * @param callback    选填 回调，返回分类列表
     */
    public void getCategoryTree(@ContentType int contentType, String zone, Map<String, String> extInfo,
            HttpCallback<List<Category>> callback) {
        doHttpDeal(mOperationService.getCategoryTree(getContentType(contentType), zone, extInfo), BaseResult::getResult, callback);
    }

    /**
     * 获取某一内容类型的整颗分类树
     *
     * @param contentType 必填 内容类型 {@link ResType}
     * @param zone        选填 区域信息
     * @param callback    选填 回调，返回分类列表
     */
    public void getCategoryTree(@ContentType int contentType, String zone, HttpCallback<List<Category>> callback) {
        getCategoryTree(contentType, zone, null, callback);
    }

    /**
     * 获取某一内容类型的整颗分类树
     *
     * @param contentType 必填 内容类型 {@link ResType}
     * @param callback    选填 回调，返回分类列表
     */
    public void getCategoryTree(@ContentType int contentType, HttpCallback<List<Category>> callback) {
        getCategoryTree(contentType, "main", callback);
    }


    /**
     * 获取某一内容类型的根分类列表
     *
     * @param contentType 必填 类容类型 {@link ResType}
     * @param zone        选填 区域信息
     * @param extInfo     选填 拓展信息
     * @param callback    选填 回调返回分类列表
     */
    public void getCategoryRoot(@ContentType int contentType, String zone, Map<String, String> extInfo,
            HttpCallback<List<Category>> callback) {
        doHttpDeal(mOperationService.getCategoryRoot(getContentType(contentType), zone, extInfo), BaseResult::getResult, callback);
    }

    /**
     * 获取某一内容类型的根分类列表
     *
     * @param contentType 必填 类容类型 {@link ResType}
     * @param zone        选填 区域信息
     * @param callback    选填 回调返回分类列表
     */
    public void getCategoryRoot(@ContentType int contentType, String zone, HttpCallback<List<Category>> callback) {
        getCategoryRoot(contentType, zone, null, callback);
    }

    /**
     * 获取某一内容类型的子分类列表
     *
     * @param contentType 必填 类容类型 {@link ResType}
     * @param callback    选填 回调返回分类列表
     */
    public void getCategoryRoot(@ContentType int contentType, HttpCallback<List<Category>> callback) {
        getCategoryRoot(contentType, "main", callback);
    }

    /**
     * 获取某一内容类型的子分类列表
     *
     * @param parentCode 必填 分类父编码
     * @param extInfo    选填 拓展信息
     * @param callback   选填 回调 返回叶子分类列表
     */
    public void getSubcategoryList(String parentCode, Map<String, String> extInfo,
            HttpCallback<List<LeafCategory>> callback) {
        doHttpDeal(mOperationService.getSubcategoryList(parentCode, extInfo), BaseResult::getResult, callback);
    }

    /**
     * 获取某一内容类型的子分类列表
     *
     * @param parentCode 必填 分类父编码
     * @param callback   选填 回调 返回叶子分类列表
     */
    public void getSubcategoryList(String parentCode, HttpCallback<List<LeafCategory>> callback) {
        getSubcategoryList(parentCode, null, callback);
    }

    /**
     * 获取某一内容类型的子分类列表。如果有更多层级，子分类可能包含category或category与LeafCategory同时存在情况。
     *
     * @param parentCode 必填 分类父编码
     * @param extInfo    选填 拓展信息
     * @param callback   选填 回调 返回子分类列表
     */
    public void getSubcategoryListForMoreLevels(String parentCode, Map<String, String> extInfo,
            HttpCallback<List<Category>> callback) {
        doHttpDeal(mOperationService.getSubcategoryListForMoreLevels(parentCode, extInfo), BaseResult::getResult, callback);
    }

    /**
     * 根据分类编码获取分类成员数量
     *
     * @param code     选填 分类编码
     * @param extInfo  选填 额外信息
     * @param callback 选填 回调 返回分类成员的数量
     */
    public void getCategoryMemberNum(String code, Map<String, String> extInfo, HttpCallback<Integer> callback) {
        doHttpDeal(mOperationService.getCategoryMemberNum(code, extInfo),
                baseResult -> {
                    MemberNum memberNum = baseResult.getResult();
                    return memberNum != null ? Integer.valueOf(memberNum.getMembersNum()) : 0;
                }, callback);
    }
    /**
     * 根据分类编码获取分类成员数量
     *
     * @param code     分类编码
     * @param callback 选填 回调 返回分类成员的数量
     */
    public void getCategoryMemberNum(String code, HttpCallback<Integer> callback) {
        getCategoryMemberNum(code, null, callback);
    }

    /**
     * 分页获取分类成员列表
     *
     * @param code     必填 分类编码
     * @param pageNum  必填 请求的页码 1、2、3....
     * @param pageSize 必填 每页请求个数
     * @param callback 选填 回调返回分类成员列表
     */
    public void getCategoryMemberList(String code, int pageNum, int pageSize,
            HttpCallback<BasePageResult<List<CategoryMember>>> callback) {
        doHttpDeal(
                mOperationService.getCategoryMemberList(code, pageNum, pageSize),
                BaseResult::getResult,
                callback);
    }

    /**
     * 获取整个栏目树，包括根栏目和子栏目
     *
     * @param isWithMembers 选填 是否要成员(false表示不要成员)，不填表示不获取成员
     * @param extInfo       选填 拓展信息
     * @param zone          选填 区域信息
     * @param callback      选填 回调返回类目组列表
     */
    public void getColumnTree(boolean isWithMembers, String zone, Map<String, String> extInfo,
            HttpCallback<List<ColumnGrp>> callback) {
        doHttpDeal(mOperationService.getColumnTree(isWithMembers ? 1 : 0, zone, extInfo), BaseResult::getResult,
                callback);
    }
    /**
     * 获取整个栏目树，包括根栏目和子栏目
     *
     * @param isWithMembers 选填 是否要成员(false表示不要成员)，不填表示不获取成员
     * @param zone          选填 区域信息
     * @param callback      选填 回调返回类目组列表
     */
    public void getColumnTree(boolean isWithMembers, String zone, HttpCallback<List<ColumnGrp>> callback) {
        getColumnTree(isWithMembers, zone, null, callback);
    }
    /**
     * 获取整个栏目树，包括根栏目和子栏目
     *
     * @param isWithMembers 选填 是否要成员(false表示不要成员)，不填表示不获取成员
     * @param callback      选填 回调返回类目组列表
     */
    public void getColumnTree(boolean isWithMembers, HttpCallback<List<ColumnGrp>> callback) {
        getColumnTree(isWithMembers, "main", callback);
    }
    /**
     * 获取不包含栏目成员的整个栏目树，包括根栏目和子栏目
     *
     * @param callback      选填 回调返回类目组列表
     */
    public void getColumnTree(HttpCallback<List<ColumnGrp>> callback) {
        getColumnTree(false, callback);
    }

    /**
     * 根据父栏目编码获取下级栏目列表
     *
     * @param parentCode 必填 栏目父编码
     * @param extInfo    选填 拓展信息
     * @param zone       选填 区域信息
     * @param callback   选填 回调返回栏目列表
     */
    public void getSubcolumnList(String parentCode, String zone, Map<String, String> extInfo,
            HttpCallback<List<Column>> callback) {
        doHttpDeal(mOperationService.getSubcolumnList(parentCode, zone, extInfo), BaseResult::getResult, callback);
    }
    /**
     * 根据父栏目编码获取下级栏目列表
     *
     * @param parentCode 必填 栏目父编码
     * @param zone       选填 区域信息
     * @param callback   选填 回调返回栏目列表
     */
    public void getSubcolumnList(String parentCode, String zone, HttpCallback<List<Column>> callback) {
        getSubcolumnList(parentCode, zone, null, callback);
    }
    /**
     * 根据父栏目编码获取下级栏目列表
     *
     * @param parentCode 必填 栏目父编码
     * @param callback   选填 回调返回栏目列表
     */
    public void getSubcolumnList(String parentCode, HttpCallback<List<Column>> callback) {
        getSubcolumnList(parentCode, "main", callback);
    }

    /**
     * 根据栏目编码获取栏目成员列表
     *
     * @param code     必填 栏目编码
     * @param extInfo  选填 拓展信息
     * @param callback 选填 回调返回栏目成员列表
     */
    public void getColumnMemberList(String code, Map<String, String> extInfo,
            HttpCallback<List<ColumnMember>> callback) {
        doHttpDeal(mOperationService.getColumnMemberList(code, extInfo), BaseResult::getResult, callback);
    }
    /**
     * 根据父栏目编码获取栏目成员列表
     *
     * @param code     必填 栏目编码
     * @param callback 选填 回调返回栏目成员列表
     */
    public void getColumnMemberList(String code, HttpCallback<List<ColumnMember>> callback) {
        getColumnMemberList(code, null, callback);
    }

    private int getContentType(int contentType) {
        //（0.综合 1.专辑 2.在线广播 3.直播 4.AI电台 5.QQ音乐）
        switch (contentType) {
            case ResType.TYPE_ALBUM:
                return 1;
            case ResType.TYPE_BROADCAST:
                return 2;
            case ResType.TYPE_LIVE:
                return 3;
            case ResType.TYPE_RADIO:
                return 4;
            case ResType.TYPE_QQ_MUSIC:
                return 5;
            case ResType.TYPE_NEWS:
                return 6;
            case ResType.TYPE_ALL:
                return 0;
            default:
        }
        return ResType.TYPE_INVALID;
    }

    //---------------------------------------以下接口已过时-----------------------------------

    /**
     * 获取栏目列表
     *
     * @param parentCode  必填 获取哪一级栏目的code(服务器返回的{@link ColumnGrp#code})，0代表请求顶层栏目,
     * @param isRecursive 必填 是否获取子栏目和栏目成员，1代表获取，0代表只获取一层子栏目
     * @param lng         经度
     * @param lat         纬度
     * @param zone        分区
     * @param extraInfo   额外信息，暂时无用
     * @deprecated
     */
    @Deprecated
    public void getColumnList(String parentCode, boolean isRecursive, String lng, String lat, String zone,
            Map<String, String> extraInfo, HttpCallback<List<ColumnGrp>> callback) {
        RequestBody requestBody = getColumnRequestBody(parentCode, isRecursive, lng, lat, zone, extraInfo);
        doHttpDeal(mOperationService.getColumnList(requestBody), BaseResult::getResult, callback);
    }

    /**
     * 获取栏目列表(同步)
     *
     * @param parentCode  必填 获取哪一级栏目的code(服务器返回的{@link ColumnGrp#code})，0代表请求顶层栏目,
     * @param isRecursive 必填 是否获取子栏目和栏目成员，1代表获取，0代表只获取一层子栏目
     * @param lng         经度
     * @param lat         纬度
     * @param zone        分区
     * @param extraInfo   额外信息，暂时无用
     */
    @Deprecated
    public List<ColumnGrp> getColumnListSync(String parentCode, boolean isRecursive, String lng, String lat,
            String zone,
            Map<String, String> extraInfo) {
        RequestBody requestBody = getColumnRequestBody(parentCode, isRecursive, lng, lat, zone, extraInfo);
        BaseResult<List<ColumnGrp>> baseResult = doHttpDealSync(mOperationService.getColumnListSync(requestBody));
        return baseResult != null ? baseResult.getResult() : null;
    }

    /**
     * 获取栏目列表
     *
     * @param parentCode  必填 获取哪一级栏目，0代表请求顶层栏目
     * @param isRecursive 必填 是否获取子栏目和栏目成员，true代表获取，false代表只获取一层子栏目
     */
    @Deprecated
    public void getColumnList(String parentCode, boolean isRecursive, HttpCallback<List<ColumnGrp>> callback) {
        getColumnList(parentCode, isRecursive, mProfileLazy.get().getLng(), mProfileLazy.get().getLat(), null,
                null, callback);
    }

    /**
     * 获取栏目列表(同步)
     *
     * @param parentCode  必填 获取哪一级栏目，0代表请求顶层栏目
     * @param isRecursive 必填 是否获取子栏目和栏目成员，true代表获取，false代表只获取一层子栏目
     */
    @Deprecated
    public List<ColumnGrp> getColumnListSync(String parentCode, boolean isRecursive) {
        return getColumnListSync(parentCode, isRecursive, mProfileLazy.get().getLng(), mProfileLazy.get().getLat(),
                null, null);
    }

    /**
     * 获取栏目列表
     *
     * @param parentCode  必填 获取哪一级栏目，0代表请求顶层栏目
     * @param isRecursive 必填 是否获取子栏目和栏目成员，true代表获取，false代表只获取一层子栏目
     * @param zone        分区
     */
    @Deprecated
    public void getColumnList(String parentCode, boolean isRecursive, String zone,
            HttpCallback<List<ColumnGrp>> callback) {
        getColumnList(parentCode, isRecursive, mProfileLazy.get().getLng(), mProfileLazy.get().getLat(), zone,
                null, callback);
    }


    /**
     * 获取栏目列表(同步)
     *
     * @param parentCode  必填 获取哪一级栏目，0代表请求顶层栏目
     * @param isRecursive 必填 是否获取子栏目和栏目成员，true代表获取，false代表只获取一层子栏目
     * @param zone        分区
     */
    @Deprecated
    public List<ColumnGrp> getColumnListSync(String parentCode, boolean isRecursive, String zone) {
        return getColumnListSync(parentCode, isRecursive, mProfileLazy.get().getLng(), mProfileLazy.get().getLat(),
                zone, null);
    }

    /**
     * 获取分类列表 可以设置是否包含分类成员
     *
     * @param parentCode    必填 获取哪一级分类，0代表请求顶层分类
     * @param isRecursive   必填 是否获取子分类和栏目成员，1代表获取，0代表只获取一层子栏目
     * @param contentType   内容类型  1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台
     * @param isWithMembers 是否包含成员：true是；false否
     * @param pageNum       分类成员页码，选择包含成员时，必传
     * @param pageSize      分类成员分页大小，选择包含成员时，必传
     * @param lng           经度
     * @param lat           纬度
     * @param zone          分区
     * @param extraInfo     额外信息，暂时无用
     */
    @Deprecated
    public void getCategoryList(String parentCode, boolean isRecursive, int contentType,
            boolean isWithMembers, int pageNum, int pageSize, String lng, String lat,
            String zone, Map<String, String> extraInfo, HttpCallback<List<Category>> callback) {
        OperationSearchParameterDTO columnSearchParameter = new OperationSearchParameterDTO();
        columnSearchParameter.setParentCode(parentCode);
        columnSearchParameter.setIsRecursive(isRecursive ? 1 : 0);
        if (contentType >= 0) {
            columnSearchParameter.setContentType(contentType);
        }
        columnSearchParameter.setWithMembers(isWithMembers ? 1 : 0);
        if (isWithMembers) {
            columnSearchParameter.setPageNum(pageNum);
            columnSearchParameter.setPageSize(pageSize);
        }
        columnSearchParameter.setLng(lng);
        columnSearchParameter.setLat(lat);
        columnSearchParameter.setZone(zone);
        columnSearchParameter.setExtInfo(extraInfo);
        String body = mGsonLazy.get().toJson(columnSearchParameter);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
        doHttpDeal(mOperationService.getCategoryList(requestBody), BaseResult::getResult, callback);
    }

    /**
     * 获取分类列表 不包含分类成员
     *
     * @param parentCode  必填 获取哪一级分类，0代表请求顶层分类
     * @param isRecursive 必填 是否获取子分类和栏目成员，true代表获取，false代表只获取一层子栏目
     * @param contentType 选填 内容类型  1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台
     */
    @Deprecated
    public void getCategoryList(String parentCode, boolean isRecursive, int contentType,
            HttpCallback<List<Category>> callback) {
        getCategoryList(parentCode, isRecursive, contentType, false, 0, 0, mProfileLazy.get().getLng(),
                mProfileLazy.get().getLat(), null, null, callback);
    }

    /**
     * 获取包含分类成员的分类列表
     *
     * @param parentCode  必填 获取哪一级分类，0代表请求顶层分类
     * @param isRecursive 必填 是否获取子分类和栏目成员，true代表获取，false代表只获取一层子栏目
     * @param contentType 选填 内容类型  1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台
     * @param pageNum     必填 分类成员页码，
     * @param pageSize    必填 分类成员分页大小
     */
    @Deprecated
    public void getCategoryList(String parentCode, boolean isRecursive, int contentType, int pageNum, int pageSize,
            HttpCallback<List<Category>> callback) {
        getCategoryList(parentCode, isRecursive, contentType, true, pageNum, pageSize, mProfileLazy.get().getLng(),
                mProfileLazy.get().getLat(), null, null, callback);
    }

    /**
     * 获取包含分类成员的分类列表
     *
     * @param parentCode    必填 获取哪一级分类，0代表请求顶层分类
     * @param isRecursive   必填 是否获取子分类和栏目成员，true代表获取，false代表只获取一层子栏目
     * @param isWithMembers 是否包含成员：true是；false否
     * @param contentType   选填 内容类型  1:专辑，2:在线广播，3:直播，4:AI电台，5:QQ音乐电台
     * @param pageNum       必填 分类成员页码，
     * @param pageSize      必填 分类成员分页大小
     */
    @Deprecated
    public void getCategoryList(String parentCode, boolean isRecursive, int contentType, boolean isWithMembers,
            int pageNum, int pageSize,
            HttpCallback<List<Category>> callback) {
        getCategoryList(parentCode, isRecursive, contentType, isWithMembers, pageNum, pageSize,
                mProfileLazy.get().getLng(),
                mProfileLazy.get().getLat(), null, null, callback);
    }


    /**
     * 获取分类列表
     *
     * @param parentCode  获取哪一级分类，0代表请求顶层分类
     * @param isRecursive 是否获取子分类和栏目成员，true代表获取，false代表只获取一层子栏目
     */
    @Deprecated
    private void getCategoryList(String parentCode, boolean isRecursive, HttpCallback<List<Category>> callback) {
        getCategoryList(parentCode, isRecursive, 0, callback);
    }

    /**
     * 根据分类id获取分类信息
     *
     * @param code 分类ID
     */
    @Deprecated
    public void getCategoryInfo(String code, HttpCallback<Category> callback) {
        doHttpDeal(mOperationService.getCategoryInfo(code), BaseResult::getResult, callback);
    }

    /**
     * 获取分类成员列表
     *
     * @param code     必填 分类code
     * @param callback 回调
     */
    @Deprecated
    public void getCategoryMemberList(String code, HttpCallback<List<CategoryMember>> callback) {
        doHttpDeal(mOperationService.getCategoryMemberList(code),
                baseResult -> {
                    BasePageResult<List<CategoryMember>> basePageResult = baseResult.getResult();
                    return basePageResult != null ? basePageResult.getDataList() : null;
                }, callback);
    }

    private RequestBody getColumnRequestBody(String parentCode, boolean isRecursive, String lng, String lat,
            String zone, Map<String, String> extraInfo) {
        OperationSearchParameterDTO columnSearchParameter = new OperationSearchParameterDTO();
        columnSearchParameter.setParentCode(parentCode);
        columnSearchParameter.setIsRecursive(isRecursive ? 1 : 0);
        columnSearchParameter.setLng(lng);
        columnSearchParameter.setLat(lat);
        columnSearchParameter.setZone(zone);
        columnSearchParameter.setExtInfo(extraInfo);
        String body = mGsonLazy.get().toJson(columnSearchParameter);
        return RequestBody.create(MediaType.parse("application/json"), body);
    }
}
