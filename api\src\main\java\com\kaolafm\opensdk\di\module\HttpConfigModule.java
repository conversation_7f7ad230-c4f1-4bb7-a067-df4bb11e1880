package com.kaolafm.opensdk.di.module;

import com.google.gson.TypeAdapterFactory;
import com.kaolafm.opensdk.api.UserUrlHandler;
import com.kaolafm.opensdk.account.token.AccessTokenManager;
import com.kaolafm.opensdk.account.token.QQMusicAccessTokenCache;
import com.kaolafm.opensdk.account.token.TokenCache;
import com.kaolafm.opensdk.api.init.VerifyActivationImpl;
import com.kaolafm.opensdk.api.login.internal.KaolaTokenRefresh;
import com.kaolafm.opensdk.api.music.qq.QQMusicTokenInterceptor;
import com.kaolafm.opensdk.api.operation.model.category.AlbumCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.BroadcastCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.Category;
import com.kaolafm.opensdk.api.operation.model.category.CategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.LeafCategory;
import com.kaolafm.opensdk.api.operation.model.category.LiveProgramCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioCategoryMember;
import com.kaolafm.opensdk.api.operation.model.category.RadioQQMusicCategoryMember;
import com.kaolafm.opensdk.api.operation.model.column.AlbumDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.AudioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.BroadcastDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.CategoryColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.Column;
import com.kaolafm.opensdk.api.operation.model.column.ColumnGrp;
import com.kaolafm.opensdk.api.operation.model.column.ColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.LiveProgramDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.RadioQQMusicDetailColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.SearchResultColumnMember;
import com.kaolafm.opensdk.api.operation.model.column.WebViewColumnMember;
import com.kaolafm.opensdk.di.qualifier.AccessTokenQualifier;
import com.kaolafm.opensdk.di.qualifier.HttpInterceptor;
import com.kaolafm.opensdk.http.core.HttpBeforeHandler;
import com.kaolafm.opensdk.http.core.RuntimeTypeAdapterFactory;
import com.kaolafm.opensdk.http.core.TokenRefresh;
import com.kaolafm.opensdk.http.core.VerifyActivation;

import dagger.Binds;
import dagger.Module;
import dagger.Provides;
import dagger.multibindings.IntoMap;
import dagger.multibindings.IntoSet;
import dagger.multibindings.StringKey;
import okhttp3.Interceptor;

/**
 * K-radio SDK的网络请求相关的全局配置
 *
 * <AUTHOR>
 * @date 2018/4/17
 */
@Module(includes = CommonHttpCfgModule.class)
public abstract class HttpConfigModule {

    @Binds
    @IntoSet
    abstract HttpBeforeHandler bindVerifyActivationHandler(VerifyActivationImpl verifyActivation);

    @Binds
    abstract VerifyActivation bindVerifyActivation(VerifyActivationImpl verifyActivation);

    @Binds
    @IntoSet
    @HttpInterceptor
    abstract Interceptor bindQQMusicTokenInterceptor(QQMusicTokenInterceptor tokenInterceptor);

    @Binds
    @IntoSet
    abstract HttpBeforeHandler bindAccountUrlInterceptor(UserUrlHandler userUrlHandler);

    @Binds
    abstract TokenRefresh bindTokenRefresh(KaolaTokenRefresh kaolaTokenRefresh);

    @Provides
    @IntoSet
    static TypeAdapterFactory provideCategoryTypeFactory() {
        return RuntimeTypeAdapterFactory.of(Category.class)
                .registerSubtype(Category.class)
                .registerSubtype(LeafCategory.class);
    }

    @Provides
    @IntoSet
    static TypeAdapterFactory provideCategoryMemberTypeFactory() {
        return RuntimeTypeAdapterFactory.of(CategoryMember.class)
                .registerSubtype(AlbumCategoryMember.class, "AlbumDetailTbCategoryMember")
                .registerSubtype(BroadcastCategoryMember.class, "BroadcastDetailTbCategoryMember")
                .registerSubtype(LiveProgramCategoryMember.class, "LiveProgramDetailTbCategoryMember")
                .registerSubtype(RadioCategoryMember.class, "RadioDetailTbCategoryMember")
                .registerSubtype(RadioQQMusicCategoryMember.class, "RadioQQDetailTbCategoryMember");
    }

    @Provides
    @IntoSet
    static TypeAdapterFactory provideColumnGrpTypeFactory() {
        return RuntimeTypeAdapterFactory.of(ColumnGrp.class)
                .registerSubtype(ColumnGrp.class)
                .registerSubtype(Column.class);
    }

    @Provides
    @IntoSet
    static TypeAdapterFactory provideColumnMemberTypeFactory() {
        return RuntimeTypeAdapterFactory.of(ColumnMember.class)
                .registerSubtype(AlbumDetailColumnMember.class)
                .registerSubtype(AudioDetailColumnMember.class)
                .registerSubtype(BroadcastDetailColumnMember.class)
                .registerSubtype(CategoryColumnMember.class)
                .registerSubtype(LiveProgramDetailColumnMember.class)
                .registerSubtype(RadioDetailColumnMember.class)
                .registerSubtype(RadioQQMusicDetailColumnMember.class)
                .registerSubtype(SearchResultColumnMember.class)
                .registerSubtype(WebViewColumnMember.class);
    }

    /**
     * QQMusicAccessTokenCache实例注入到map集合中，在{@link AccessTokenManager}中使用。实现接口只能用bind这个方式
     * @param tokenCache
     * @return
     */
    @Binds
    @IntoMap
    @AccessTokenQualifier
    @StringKey(AccessTokenManager.TOKEN_QQMUSIC)
    abstract TokenCache bindQQMusicAccessTokenCache(QQMusicAccessTokenCache tokenCache);

}
