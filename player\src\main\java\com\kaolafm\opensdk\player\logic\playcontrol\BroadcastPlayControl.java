package com.kaolafm.opensdk.player.logic.playcontrol;

import com.kaolafm.base.utils.DateUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.broadcast.BroadcastRequest;
import com.kaolafm.opensdk.api.broadcast.ProgramDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerStateListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.BroadcastPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.TimeInfoData;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.concurrent.TimeUnit;

import io.reactivex.Flowable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;


/**
 * <AUTHOR> on 2019/4/10.
 */

public class BroadcastPlayControl extends BasePlayControl {
    /**
     * 等待获取下一首广播时间
     */
    //private static final int WAIT_TIMER = 10000;
    private long progressTime;
    private long startTime;
    private long endTime;
    private long curServiceTime;
    private long livingTotalTime;
    private Disposable mDisposable;
    private IPlayerStateListener mIPlayerStateListener;

    @Override
    public void setPlayStateListener(BasePlayStateListener iPlayerStateListener) {
        mIPlayerStateListener = iPlayerStateListener;
    }

    public BroadcastPlayControl() {
        super();
    }

    @Override
    public void start(int type, PlayItem playItem) {
        mPlayItem = playItem;
        if (playItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            stopTimer();
            return;
        }
        BroadcastPlayItem playItemTemp = (BroadcastPlayItem) playItem;
        // 新增：回放url为空时自动刷新获取
        if (playItemTemp.getStatus() == PlayerConstants.BROADCAST_STATUS_PLAYBACK && (playItemTemp.getPlayUrl() == null || playItemTemp.getPlayUrl().isEmpty())) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "回放url为空，尝试重新获取");
            new BroadcastRequest().getBroadcastProgramDetails(playItemTemp.getAudioId(), new HttpCallback<ProgramDetails>() {
                @Override
                public void onSuccess(ProgramDetails programDetails) {
                    String newUrl = programDetails != null ? programDetails.getBackLiveUrl() : null;
                    if (newUrl != null && !newUrl.isEmpty() && !newUrl.contains("m3u8")) {
                        playItemTemp.setPlayUrl(newUrl);
                        stopTimer();
                        PlayerLogUtil.log(getClass().getSimpleName(), "start", "重新获取回放url成功，重新播放");
                        BroadcastPlayControl.super.start(type, playItemTemp);
                    } else {
                        PlayerLogUtil.log(getClass().getSimpleName(), "start", "重新获取回放url失败，依然为空");
                        if (mIPlayerStateListener != null) {
                            mIPlayerStateListener.onPlayerFailed(playItemTemp, PlayerConstants.ERROR_CODE_BACK_PLAY_URL_NOT_GENERATE, 404);
                        }
                    }
                }
                @Override
                public void onError(ApiException e) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "start", "重新获取回放url异常: " + e);
                    if (mIPlayerStateListener != null) {
                        mIPlayerStateListener.onPlayerFailed(playItemTemp, 123456, 404);
                    }
                }
            });
            return;
        }




        if (playItemTemp.getStatus() == PlayerConstants.BROADCAST_STATUS_LIVING) {
            TimeInfoData timeInfoData = playItemTemp.getTimeInfoData();
            curServiceTime = timeInfoData.getCurSystemTime();
            startTime = timeInfoData.getStartTime();
            endTime = timeInfoData.getFinishTime();
            livingTotalTime = endTime - startTime;
            progressTime = curServiceTime - startTime;
            if (progressTime < 0) {
                progressTime = 0;
            }
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "live count time: curServiceTime" + curServiceTime + ", startTime = " + timeInfoData.getStartTime() + ", endTime = " + timeInfoData.getEndTime());
            startTimer();
        } else {
            stopTimer();
        }
        if (isInterceptBroadcastAutoPlay()) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "intercept normal play");
            setPlayUrl(mPlayItem.getPlayUrl(), 0, 0);
            mIPlayerStateListener.onPlayerPaused(mPlayItem);
        } else {
            if(playItemTemp.getTimeInfoData().getStartTime() > DateUtil.getServerTime()){
                mIPlayerStateListener.onPlayerFailed(playItem, 404, 404);
                return;
            }
            super.start(type, playItem);
        }
    }

    private boolean isInterceptBroadcastAutoPlay() {
        if (mPlayItem.getType() != PlayerConstants.RESOURCES_TYPE_BROADCAST) {
            return false;
        }
        String type = mPlayItem.getMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
        if (!StringUtil.isEmpty(type)) {
            mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            return true;
        }
        return false;
    }

    private void notifyProgress(long progressTime, long total) {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "广播 直播通知 进度 : progressTime: " + progressTime + " total = "+ total);
        mIPlayerStateListener.onProgress(mPlayItem, (int) progressTime, (int) total);
    }

    private void notifyPause() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        mIPlayerStateListener.onPlayerPaused(mPlayItem);
    }

    private void notifyPlayEnd() {
        if (PlayerPreconditions.checkNull(mIPlayerStateListener)) {
            return;
        }
        PlayerLogUtil.log(getClass().getSimpleName(), "notifyPlayEnd");
        mIPlayerStateListener.onPlayerEnd(mPlayItem);
    }

    private void startTimer() {
        stopTimer();
        mDisposable = Flowable.interval(1, TimeUnit.SECONDS).onBackpressureDrop()
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(aLong -> onTimer());
    }

    private void onTimer() {
        progressTime = progressTime + 1000;
        // Log.i(PlayerConstants.LOG_PROGRESS_TAG, "直播倒计时时间为: " + progressTime + " 总时间: " + livingTotalTime);
        if (progressTime > livingTotalTime) {
            ((BroadcastPlayItem) mPlayItem).setStatus(PlayerConstants.BROADCAST_STATUS_PLAYBACK);
            if (isPlaying()) {
                reset();
                pause();
                mPlayItem.removeMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY);
            } else {
                PlayerLogUtil.log(getClass().getSimpleName(), "onTimer","broadcast play end, is pause, loading data");
                mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_BROADCAST_NO_PLAY, "1");
            }
            notifyPlayEnd();
            stopTimer();
        } else {
            notifyProgress(progressTime, livingTotalTime);
        }
    }


    public void stopTimer() {
        if (mDisposable != null && !mDisposable.isDisposed()) {
            mDisposable.dispose();
            mDisposable = null;
        }
    }


    @Override
    public void release() {
        super.release();
        stopTimer();
    }
}
