package com.kaolafm.opensdk.api.search;

import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.search.model.VoiceSearchResult;
import com.kaolafm.opensdk.http.core.HttpCallback;

/**
 * 语音搜索
 *
 * <AUTHOR>
 * @date 2018/8/2
 * @deprecated 已过时，请使用{@link SearchRequest}
 */
@Deprecated
public class VoiceSearchRequest extends BaseRequest {

    private final SearchRequest mSearchRequest;

    public VoiceSearchRequest() {
        mSearchRequest = new SearchRequest();
    }

    /**
     * 语义搜索，包括了所有参数
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:sibichi;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param origJson    可填 语音商原始json串
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 在线广播
     * @param tag         可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keyword     必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     * @deprecated 已过时，请使用{@link SearchRequest#searchBySemantics(String, int, String, int, int, String, String, String, String, String, String, HttpCallback)}
     */
    @Deprecated
    public void searchBySemantics(
            String voiceSource,
            int qualityType,
            String origJson,
            int field,
            int tag,
            String artist,
            String audioName,
            String albumName,
            String category,
            String keyword,
            String text,
            HttpCallback<VoiceSearchResult> callback) {
        mSearchRequest.searchBySemantics(voiceSource, qualityType, origJson, field, tag, artist, audioName, albumName, category, keyword, text, callback);
    }

    /**
     * 语义搜索，包括了所有参数
     *
     * @param voiceSource 必填 语音来源 公司标识,考拉:kaola;同行者:txzing;思必驰:sibichi;问问:wenwen;蓦然:moran;科大 讯飞:kedaxunfei;
     * @param qualityType 可填 音频质量要求,0:低;1:高;
     * @param origJson    可填 语音商原始json串
     * @param field       必填 场景类别 1：音乐，2：综合; 6: 在线广播
     * @param tag         可填 参数可信标识 0：不可信，1：可信；为1时，表示场景分类和其他字段等信息可信度高。
     * @param artist      可填 艺术家
     * @param audioName   可填 音频名称
     * @param albumName   可填 专辑名称
     * @param category    可填 分类
     * @param keyword     必填 关键词 多个关键词以英文逗号“,”分隔
     * @param text        必填 用户声控的原始串
     * @param language    可填 语言 暂不支持
     * @param freq        可填 电台频率 暂不支持
     * @param area        可填 搜索text中的地点 暂不支持
     * @deprecated 已过时，请使用{@link SearchRequest#searchBySemantics(String, int, String, int, int, String, String, String, String, String, String, String, String, String, HttpCallback)}
     */
    @Deprecated
    public void searchBySemantics(
            String voiceSource,
            int qualityType,
            String origJson,
            int field,
            int tag,
            String artist,
            String audioName,
            String albumName,
            String category,
            String keyword,
            String text,
            String language,
            String freq,
            String area,
            HttpCallback<VoiceSearchResult> callback) {
        searchBySemantics(voiceSource, qualityType, origJson, field, tag, artist, audioName, albumName, category,
                keyword, text, callback);
    }
}
