package com.kaolafm.opensdk.player.logic.playcontrol;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;

import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.player.core.PlayerService;
import com.kaolafm.opensdk.player.core.listener.IPlayerBufferProgressListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerInitCompleteListener;
import com.kaolafm.opensdk.player.core.listener.IPlayerStateCoreListener;
import com.kaolafm.opensdk.player.core.listener.OnAudioFocusChangeInter;
import com.kaolafm.opensdk.player.core.listener.OnPlayStart;
import com.kaolafm.opensdk.player.core.model.AAudioFocus;
import com.kaolafm.opensdk.player.core.model.AudioFadeConfig;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.factory.PlayControlFactory;
import com.kaolafm.opensdk.player.logic.listener.BasePlayStateListener;
import com.kaolafm.opensdk.player.logic.listener.IPlayerInteractionFiredListener;
import com.kaolafm.opensdk.player.logic.model.PlayItemConstants;
import com.kaolafm.opensdk.player.logic.model.item.InvalidPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.LivePlayItem;
import com.kaolafm.opensdk.player.logic.model.item.TempTaskPlayItem;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;
import com.kaolafm.opensdk.player.logic.util.SDKReportManager;
import com.kaolafm.report.util.ReportConstants;

import static com.kaolafm.opensdk.player.logic.util.PlayerConstants.RESOURCES_TYPE_ALBUM;


/**
 * <AUTHOR> on 2019/3/18.
 */

public class PlayControl {
    private PlayerService.PlayerServiceBinder mPlayerBinder;

    private PlayItem mPlayItem;
    private LivePlayItem mLivePlayItem;

    private TempTaskPlayItem mTempTaskPlayItem;

    private IPlayControl mIPlayControl;

    private BasePlayStateListener mIPlayControlListener;

    private IPlayerInitCompleteListener mInitCompleteListener;

    private IPlayerInteractionFiredListener mPlayerInteractionFiredListener;

    private Context mContext;
    private int mType;
    /**
     * 是否播放失败, 调用ijk播放, 由于无网等问题导致播放失败, 等有网时, 需要调用start重新走播放逻辑, 不能调用play.
     */
    private boolean isPlayError;

    private PlayControl() {
    }

    private static class PlayInstance {
        private static final PlayControl PLAYER = new PlayControl();
    }

    public static PlayControl getInstance() {
        return PlayControl.PlayInstance.PLAYER;
    }

    public void init(Context context, BasePlayStateListener iPlayControlListener, IPlayerInitCompleteListener initCompleteListener) {
        mContext = context;
        mIPlayControlListener = iPlayControlListener;
        mInitCompleteListener = initCompleteListener;
        Intent playServiceIntent = new Intent(mContext, PlayerService.class);
        mContext.bindService(playServiceIntent,
                mPlayerServiceConnection, Context.BIND_AUTO_CREATE);
    }


    public void play() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (isPlayError && mPlayItem != null) {
            isPlayError = false;
            mIPlayControl.start(mType, mPlayItem);
        }
        mIPlayControl.play();
    }

    public void pause() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.pause();
        SDKReportManager.getInstance().reportEndPlay(ReportConstants.PLAY_CHANGE_BY_CLICK, true);
    }

    public void stop() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.stop();
    }

    public void reset() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.reset();
    }

    public void release() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.release();
    }

    public void switchPlayerStatus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (mIPlayControl.isPlaying()) {
            pause();
        } else {
            play();
        }
    }

    public void seek(int position) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.seek(position);
    }

    public long getCurrentPosition() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return -1;
        }
        return mIPlayControl.getCurrentPosition();
    }

    public void startTempTask(TempTaskPlayItem playItem) {
        mTempTaskPlayItem = playItem;
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.playTempTask(playItem.getPlayUrl());
        if (mPlayItem != null) {
            mIPlayControlListener.onPlayerPaused(mPlayItem);
        }
    }

    public void stopTempTask() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (mTempTaskPlayItem != null) {
            if (!mTempTaskPlayItem.isNeedNextInnerAction()) {
                PlayerLogUtil.log(getClass().getSimpleName(), "stopTempTask", "return");
                return;
            }
            boolean isPlaying = mTempTaskPlayItem.getPlayerIsPlaying();
            PlayerLogUtil.log(getClass().getSimpleName(), "stopTempTask", "i splaying =" + isPlaying);

            if (mPlayItem != null) {
                if (isPlaying) {
                    mIPlayControl.start(mType, mPlayItem);
                } else {
                    mIPlayControl.setPlayUrl(mPlayItem.getPlayUrl(), mPlayItem.getPosition(), mPlayItem.getDuration());
                }
                mTempTaskPlayItem = null;
                return;
            }
            mIPlayControl.reset();
            mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_END);
            mTempTaskPlayItem = null;
        }
    }

    public void setCustomAudioFocus(AAudioFocus audioFocus) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setCustomAudioFocus(audioFocus);
    }

    public void start(int type, PlayItem playItem) {
        PlayerLogUtil.log(getClass().getSimpleName(), "start", "Play control start....: " + playItem.getPlayUrl());

        mPlayItem = playItem;
        if (type != mType || PlayerPreconditions.checkNull(mIPlayControl)) {
            setIPlayControl(type);
            mType = type;
        }
        mIPlayerStateCoreListener.onPlayerPreparing(playItem.getPlayUrl());

        /**
         * 是否需要阻断继续播放行为
         */
        OnPlayStart onPlayStart = PlayerCustomizeManager.getInstance().getOnPlayStart();
        if (onPlayStart != null) {
            PlayerLogUtil.log(getClass().getSimpleName(), "start", "Play control start...intercept...");
            onPlayStart.interceptPlay(playItem, () -> {
                        PlayerLogUtil.log(getClass().getSimpleName(), "start", "intercept end, playing");
                        mTempTaskPlayItem = null;
                        mIPlayControl.start(mType, mPlayItem);
                    }
            );
            return;
        }
        mIPlayControl.start(type, playItem);
    }


    private void setIPlayControl(int type) {
        mIPlayControl = PlayControlFactory.getPlayControl(type);
        mIPlayControl.setBind(mPlayerBinder);
        mIPlayControl.setPlayStateListener(mIPlayControlListener);
    }

    public boolean requestAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return false;
        }
        return mIPlayControl.requestAudioFocus();
    }

    public boolean abandonAudioFocus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return false;
        }
        return mIPlayControl.abandonAudioFocus();
    }

    public void setPlayerInteractionFiredListener(IPlayerInteractionFiredListener interactionFiredListener) {
        mPlayerInteractionFiredListener = interactionFiredListener;
    }

    public void setCallback(BasePlayStateListener iPlayControlListener) {
        mIPlayControlListener = iPlayControlListener;
    }

    public void setLoudnessNormalization(int isActive) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setLoudnessNormalization(isActive);
    }


    public boolean isPlaying() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return false;
        }
        return mIPlayControl.isPlaying();
    }

    /**
     * 获取播放状态
     *
     * @return
     */
    public int getPlayStatus() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return PlayerConstants.TYPE_PLAYER_IDLE;
        }
        return mIPlayControl.getPlayStatus();
    }

    public void setMediaVolume(float leftVolume, float rightVolume) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }

        mIPlayControl.setMediaVolume(leftVolume, rightVolume);
    }

    public void setAudioFocusListener(OnAudioFocusChangeInter iAudioFocusListener) {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        mIPlayControl.setAudioFocusListener(iAudioFocusListener);
    }

    private ServiceConnection mPlayerServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName componentName,
                                       IBinder iBinder) {
            if (iBinder instanceof PlayerService.PlayerServiceBinder) {
                mPlayerBinder = (PlayerService.PlayerServiceBinder) iBinder;
                mPlayerBinder.initPlayer();
                mPlayerBinder.setInitCompleteListener(iPlayerInitCompleteListener);
                mPlayerBinder.setPlayerStateListener(mIPlayerStateCoreListener);
                mPlayerBinder.setPlayerBufferProgressListener(mBufferProgressListener);
                //service绑定成功后设置默认的PlayControl
                setIPlayControl(RESOURCES_TYPE_ALBUM);
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName componentName) {
        }
    };

    private IPlayerInitCompleteListener iPlayerInitCompleteListener = flag -> {
        if (mInitCompleteListener != null) {
            mInitCompleteListener.onPlayerInitComplete(true);
        }
    };

    private IPlayerBufferProgressListener mBufferProgressListener = (downloadSize, totalSize) -> {
        if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
            return;
        }
        mIPlayControlListener.onDownloadProgress(mPlayItem, downloadSize, totalSize);
    };

    private IPlayerStateCoreListener mIPlayerStateCoreListener = new IPlayerStateCoreListener() {

        @Override
        public void onIdle(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_IDLE);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onIdle(mPlayItem);
        }

        @Override
        public void onPlayerPreparingComplete(String url) {

        }

        @Override
        public void onPlayerPreparing(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_PREPARING);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onPlayerPreparing(mPlayItem);
        }

        @Override
        public void onPlayerPlaying(String url) {
            isPlayError = false;
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_PLAYING);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mTempTaskPlayItem = null;
            mIPlayControlListener.onPlayerPlaying(mPlayItem);
        }

        @Override
        public void onPlayerPaused(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_PAUSED);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onPlayerPaused(mPlayItem);
        }

        @Override
        public void onProgress(String url, long progress, long total) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_PROGRESS, progress, total);
                return;
            }
            if (mPlayItem == null || StringUtil.isEmpty(mPlayItem.getPlayUrl())) {
                return;
            }
            if (StringUtil.isEmpty(url)) {
                return;
            }
            if (!url.equals(mPlayItem.getPlayUrl())) {
                return;
            }
            if (mPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_BROADCAST && mPlayItem.isLiving()) {
                return;
            }
            // 因底层服务器直播回放节目duration会有错误造成上层显示有问题，故加此判断 #29336
            if (mPlayItem.getDuration() != total) {
                mPlayItem.setDuration((int) total);
            }
            //因直播节目没有total时会造成上层进度条显示问题，故此处理 TB：#ZMKQ-1392
            if (total == 0 && mPlayItem.getType() == PlayerConstants.RESOURCES_TYPE_LIVING) {
                mLivePlayItem = (LivePlayItem) mPlayItem;
                long duration = mLivePlayItem.getTimeInfoData().getFinishTime() - mLivePlayItem.getTimeInfoData().getStartTime();
                duration = Math.min(duration, 24 * 60 * 60 * 1000);
                total = duration;
                mPlayItem.setDuration((int) total);
            }
            mPlayItem.setPosition((int) progress);
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            //  Log.i(PlayerConstants.LOG_PROGRESS_TAG, getClass().getSimpleName() + "   progress = " + progress);
            mIPlayControlListener.onProgress(mPlayItem, (int) progress, (int) total);
        }

        @Override
        public void onPlayerFailed(String url, int what, int extra, String dnsAddress) {
            if (isTempTask(url)) {
                if (mTempTaskPlayItem.getTempTaskType() == PlayerConstants.TEMP_TASK_TYPE_CLOCK) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "onPlayerFailed", "play clock, play error");
                    mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_END);
                    mTempTaskPlayItem = null;
                } else {
                    mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_FAILED);
                }
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            isPlayError = true;
            mPlayItem.addMapCacheData(PlayItemConstants.ITEM_KEY_DNS_ADDRESS, dnsAddress);
            mIPlayControlListener.onPlayerFailed(mPlayItem, what, extra);
        }


        @Override
        public void onPlayerEnd(String url) {
            if (isTempTask(url)) {
                if (mPlayItem == null) {
                    mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_END);
                    mTempTaskPlayItem = null;
                    return;
                }
                if (!mTempTaskPlayItem.isNeedNextInnerAction()) {
                    mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_END);
                    return;
                }
                PlayerLogUtil.log(getClass().getSimpleName(), "onPlayerEnd", "tempTask end, is playing =" + mTempTaskPlayItem.getPlayerIsPlaying());

                if (mTempTaskPlayItem.getPlayerIsPlaying()) {
                    mIPlayerStateCoreListener.onPlayerPreparing(mPlayItem.getPlayUrl());
                    mIPlayControl.start(mType, mPlayItem);
                } else {
                    //if (mTempTaskPlayItem.getTempTaskType() != PlayerConstants.TEMP_TASK_TYPE_CLOCK) {
                    mIPlayControl.setPlayUrl(mPlayItem.getPlayUrl(), mPlayItem.getPosition(), mPlayItem.getDuration());
                    //}
                }
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_PLAYER_END);
                mTempTaskPlayItem = null;
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onPlayerEnd(mPlayItem);
        }

        @Override
        public void onSeekStart(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_SEEK_START);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onSeekStart(mPlayItem);
        }

        @Override
        public void onSeekComplete(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_SEEK_COMPLETE);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onSeekComplete(mPlayItem);
        }

        @Override
        public void onBufferingStart(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_BUFFERING_START);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onBufferingStart(mPlayItem);
        }

        @Override
        public void onBufferingEnd(String url) {
            if (isTempTask(url)) {
                mTempTaskPlayItem.notifyStateChange(PlayerConstants.TYPE_BUFFERING_END);
                return;
            }
            if (PlayerPreconditions.checkNull(mIPlayControlListener)) {
                return;
            }
            mIPlayControlListener.onBufferingEnd(mPlayItem);
        }

        @Override
        public void onInteractionFired(String url, int position, int id) {
            if (mPlayerInteractionFiredListener != null) {
                mPlayerInteractionFiredListener.onInteractionFired(mPlayItem, position, id);
            }
        }
    };

    /**
     * 设置log无效
     */
    public void setLogInValid() {
        mIPlayControl.setLogInValid();
    }

    /**
     * 禁用淡入淡出效果
     */
    public void disableAudioFade() {
        mIPlayControl.disableAudioFade();
    }

    public void setHttpProxy(String httpProxy) {
        mIPlayControl.setHttpProxy(httpProxy);
    }

    public void clearHttpProxy() {
        mIPlayControl.clearHttpProxy();
    }

    public void clearDnsCache(boolean clearDnsCache) {
        mIPlayControl.clearDnsCache(clearDnsCache);
    }

    /**
     * 设置淡入淡出效果配置
     *
     * @param audioFadeConfig
     */
    public void setAudioFadeConfig(AudioFadeConfig audioFadeConfig) {
        mIPlayControl.setAudioFadeConfig(audioFadeConfig);
    }

    public PlayItem getCurrentTempTaskPlayItem() {
        return mTempTaskPlayItem;
    }

    public PlayItem getCurrentPlayItem() {
        if (PlayerPreconditions.checkNull(mPlayItem)) {
            return new InvalidPlayItem();
        }

        return mPlayItem;
    }

    private boolean isTempTask(String url) {
        if (StringUtil.isEmpty(url)) {
            return false;
        }
        if (mTempTaskPlayItem == null || StringUtil.isEmpty(mTempTaskPlayItem.getPlayUrl())) {
            return false;
        }
        if (mTempTaskPlayItem.getPlayUrl().equals(url)) {
            PlayerLogUtil.log(getClass().getSimpleName(), "isTempTask", "playing a temp task");
            return true;
        }
        return false;
    }

    /**
     * 当播放最后一首时, 设置为无效的Playitem
     */
    public void setInvalidPlayItem() {
        mPlayItem = new InvalidPlayItem();
    }

    public void stopBroadcastTimer() {
        if (PlayerPreconditions.checkNull(mIPlayControl)) {
            return;
        }
        if (mIPlayControl instanceof BroadcastPlayControl) {
            ((BroadcastPlayControl) mIPlayControl).stopTimer();
        }
    }


    private void unBindService() {
        try {
            mContext.unbindService(mPlayerServiceConnection);
        } catch (IllegalStateException ill) {
            ill.printStackTrace();
        } catch (IllegalArgumentException illA) {
            illA.printStackTrace();
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public void destroy() {
        if (!PlayerPreconditions.checkNull(mIPlayControl)) {
            mIPlayControl.destroy();
            return;
        }
        unBindService();
    }

    public boolean isAsyncStartExecuting() {
        if (!PlayerPreconditions.checkNull(mIPlayControl)) {
            return mIPlayControl.isAsyncStartExecuting();
        }
        return false;
    }
}
