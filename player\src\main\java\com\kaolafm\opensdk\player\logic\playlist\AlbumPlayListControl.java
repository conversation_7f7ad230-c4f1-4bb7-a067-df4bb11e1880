package com.kaolafm.opensdk.player.logic.playlist;

import com.kaolafm.base.utils.ListUtil;
import com.kaolafm.base.utils.StringUtil;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.media.AlbumRequest;
import com.kaolafm.opensdk.api.media.AudioRequest;
import com.kaolafm.opensdk.api.media.model.AlbumDetails;
import com.kaolafm.opensdk.api.media.model.AudioDetails;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;
import com.kaolafm.opensdk.player.logic.listener.IPlayListGetListener;
import com.kaolafm.opensdk.player.logic.model.CustomPlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.PlayerBuilder;
import com.kaolafm.opensdk.player.logic.model.item.model.PlayItem;
import com.kaolafm.opensdk.player.logic.playlist.innerinterface.IDataListCallback;
import com.kaolafm.opensdk.player.logic.playlist.util.PlayListUtils;
import com.kaolafm.opensdk.player.logic.util.PlayerConstants;
import com.kaolafm.opensdk.player.logic.util.PlayerLogUtil;
import com.kaolafm.opensdk.player.logic.util.PlayerPreconditions;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2019/3/18.
 */

public class AlbumPlayListControl extends BasePlayListControl {
    private static final int LOAD_DATA = 0;
    private static final int LOAD_DATA_NEXT = 1;
    private static final int LOAD_DATA_PRE = 2;
    public static final int LOAD_DATA_PAGE = 3; //获取整页数据
    private static final int LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM = 4; //获取整页数据后播放下一个
    private static final int LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM = 5; //获取整页数据后播放上一个
    private AlbumRequest mAlbumRequest;
    private AudioRequest mAudioRequest;
    private PlayerBuilder tempPlayerBuilder;
    private int mPageSize = PlayerConstants.PAGE_NUMBER_10;

    public AlbumPlayListControl() {
        super();
        mAlbumRequest = new AlbumRequest();
        mAudioRequest = new AudioRequest();
    }

    @Override
    public void initPlayList(PlayerBuilder playerBuilder, IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "album");
        if (playerBuilder.getType() == PlayerConstants.RESOURCES_TYPE_AUDIO) {
            PlayerLogUtil.log(getClass().getSimpleName(), "initPlayList", "play audio, get audio detail");
            tempPlayerBuilder = new CustomPlayerBuilder();
            ((CustomPlayerBuilder) tempPlayerBuilder).setChildId(playerBuilder.getId()).setType(playerBuilder.getType());
            getAudioInfo(iPlayListGetListener);
            return;
        }
        tempPlayerBuilder = playerBuilder;
        getAlbumInfo(iPlayListGetListener);
    }

    @Override
    public int getCurPosition() {
        if (mPosition != -1 && !isInList(mPlayItemArrayList, mCurPlayItem)) {
            return -1;
        }
        return mPosition;
    }

    @Override
    public void getNextPlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getNextPlayItem(iPlayListGetListener);
    }

    @Override
    public void getPrePlayItem(IPlayListGetListener iPlayListGetListener) {
        if (mPlayItemArrayList != null && mCurPlayItem != null && mPosition != -1) {
            boolean isInList = isInList(mPlayItemArrayList, mCurPlayItem);
            if (!isInList) {
                loadPageData(LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM,
                        mCurPlayItem.getAudioId(), 1, iPlayListGetListener);
                return;
            }
        }
        super.getPrePlayItem(iPlayListGetListener);
    }

    @Override
    public void loadNextPage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "album get next page ...");
        long albumId = string2Long(mPlaylistInfo.getId());
        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getNextPage(), new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_NEXT, listBasePageResult);
                updatePlayListContent(LOAD_DATA_NEXT, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error() {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadNextPage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, -1);
                notifyPlayListChangeError(-1);
            }
        });
    }

    @Override
    public void loadPrePage(IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "album get pre page playlist...");
        if (!mPlaylistInfo.isHasPrePage()) {
            return;
        }
        long albumId = string2Long(mPlaylistInfo.getId());

        loadPlayListData(albumId, mPlaylistInfo.getSort(), mPlaylistInfo.getPrePage(), new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                updatePlayListInfo(LOAD_DATA_PRE, listBasePageResult);
                updatePlayListContent(LOAD_DATA_PRE, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error() {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPrePage", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, -1);
                notifyPlayListChangeError(-1);
            }
        });
    }

    @Override
    public void loadPageData(int type, long audioId, int pageNum, IPlayListGetListener iPlayListGetListener){
        PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "album get page data playlist...");

        long albumId = string2Long(mPlaylistInfo.getId());

        loadPlayListData(albumId, audioId, mPlaylistInfo.getSort(), pageNum, new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                if (ListUtil.isEmpty(playItemArrayList)) {
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                updatePlayListInfo(type, listBasePageResult);
                updatePlayListContent(type, playItemArrayList, iPlayListGetListener);
            }

            @Override
            public void error() {
                PlayerLogUtil.log(getClass().getSimpleName(), "loadPageData", "get playlist error....");
                notifyPlayListGetError(iPlayListGetListener, -1);
                notifyPlayListChangeError(-1);
            }
        });
    }

    private void loadPlayListData(long albumId, int sort, int pageNum, IDataListCallback<BasePageResult<List<AudioDetails>>> iDataListCallback) {
        loadPlayListData(albumId, 0, sort, pageNum, iDataListCallback);
    }

    /**
     * 加载数据
     *
     * @param albumId
     * @param audioId
     * @param sort
     * @param pageNum
     * @param iDataListCallback
     */
    private void loadPlayListData(long albumId, long audioId, int sort, int pageNum, IDataListCallback<BasePageResult<List<AudioDetails>>> iDataListCallback) {
        mAlbumRequest.getPlaylist(albumId, audioId, sort, mPageSize, pageNum, new HttpCallback<BasePageResult<List<AudioDetails>>>() {
            @Override
            public void onSuccess(BasePageResult<List<AudioDetails>> listBasePageResult) {
                if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
                    if (iDataListCallback != null) {
                        iDataListCallback.error();
                    }
                    return;
                }
                if (iDataListCallback != null) {
                    iDataListCallback.success(listBasePageResult);
                }
            }

            @Override
            public void onError(ApiException e) {
                if (iDataListCallback != null) {
                    iDataListCallback.error();
                }
            }
        });
    }

    private int getNotifyPlayListIndex(ArrayList<PlayItem> playItemArrayList, long audioId) {
        if (audioId <= 0) {
            return 0;
        }
        for (int i = 0; i < playItemArrayList.size(); i++) {
            PlayItem playItem = playItemArrayList.get(i);
            if (PlayerPreconditions.checkNull(playItem)) {
                continue;
            }

            if (playItem.getAudioId() == audioId) {
                return i;
            }
        }

        return 0;
    }

    private void getAlbumInfo(final IPlayListGetListener iPlayListGetListener) {
        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo");
        long albumId = string2Long(tempPlayerBuilder.getId());
        long audioId = 0;
        if (tempPlayerBuilder instanceof CustomPlayerBuilder) {
            audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        }

        long finalAudioId = audioId;
        mAlbumRequest.getAlbumDetails(albumId, new HttpCallback<AlbumDetails>() {
            @Override
            public void onSuccess(AlbumDetails albumDetails) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success");

                if (PlayerPreconditions.checkNull(albumDetails)) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, but data is null");
                    notifyPlayListGetError(iPlayListGetListener, -1);
                    return;
                }
                mPlaylistInfo.setNoSubscribe(albumDetails.getNoSubscribe());
                tempPlayerBuilder.setNoSubscribe(albumDetails.getNoSubscribe());
                if (albumDetails.getIsOnline() != PlayerConstants.ALBUM_ONLINE) {
                    PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "success, offline");
                    notifyPlayListGetError(iPlayListGetListener, PlayerConstants.ERROR_CODE_ALBUM_OFFLINE);
                    return;
                }

                loadPlayListData(albumId, finalAudioId, PlayerConstants.SORT_ACS, 1, new IDataListCallback<BasePageResult<List<AudioDetails>>>() {
                    @Override
                    public void success(BasePageResult<List<AudioDetails>> listBasePageResult) {
                        if (PlayerPreconditions.checkNull(listBasePageResult) || ListUtil.isEmpty(listBasePageResult.getDataList())) {
                            PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success, but list is null");
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list success");
                        AlbumPlayListControl.super.initPlayList(tempPlayerBuilder, iPlayListGetListener);
                        updatePlayListInfo(LOAD_DATA, albumDetails, listBasePageResult);
                        ArrayList<PlayItem> playItemArrayList = PlayListUtils.audioDetailToAlbumPlayItem(listBasePageResult.getDataList(), mPlaylistInfo);
                        if (ListUtil.isEmpty(playItemArrayList)) {
                            notifyPlayListGetError(iPlayListGetListener, -1);
                            return;
                        }
                        release();
                        updatePlayListContent(LOAD_DATA, finalAudioId, playItemArrayList, iPlayListGetListener);
                    }

                    @Override
                    public void error() {
                        PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get play list error");
                        notifyPlayListGetError(iPlayListGetListener, -1);
                    }
                });
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAlbumInfo", "get album info error");
                notifyPlayListGetError(iPlayListGetListener, -1);
            }
        });

    }

    private long string2Long(String strValue) {
        long longValue = 0;
        if (!StringUtil.isEmpty(strValue)) {
            try {
                longValue = Long.parseLong(strValue);
            } catch (Exception e) {

            }
        }
        return longValue;
    }

    private void getAudioInfo(final IPlayListGetListener iPlayListGetListener) {
        long audioId = string2Long(((CustomPlayerBuilder) tempPlayerBuilder).getChildId());
        mAudioRequest.getAudioDetails(audioId, new HttpCallback<AudioDetails>() {
            @Override
            public void onSuccess(AudioDetails audioDetails) {
                //此处获取到的audioDetails中只有id，不再包含url信息
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "success");
                mPlaylistInfo.setTempId(String.valueOf(audioDetails.getAlbumId()));
                mPlaylistInfo.setTempChildId(String.valueOf(audioDetails.getAudioId()));
                tempPlayerBuilder.setId(String.valueOf(audioDetails.getAlbumId()));
                getAlbumInfo(iPlayListGetListener);
            }

            @Override
            public void onError(ApiException e) {
                PlayerLogUtil.log(getClass().getSimpleName(), "getAudioInfo", "error");
                notifyPlayListGetError(iPlayListGetListener, -1);
            }
        });
    }

    private void updatePlayListContent(int type, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        updatePlayListContent(type, 0, playItems, iPlayListGetListener);
    }

    private void updatePlayListContent(int type, long audioId, ArrayList<PlayItem> playItems, final IPlayListGetListener iPlayListGetListener) {
        int needPlayIndex = 0;
        switch (type) {
            case LOAD_DATA:
                needPlayIndex = getNotifyPlayListIndex(playItems, audioId);
                break;
            case LOAD_DATA_NEXT:

                break;
            case LOAD_DATA_PRE:
                needPlayIndex = playItems.size() - 1;
                break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (++needPlayIndex >= playItems.size()) {
                    loadNextPage(iPlayListGetListener);
                    return;
                }
                break;
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
                needPlayIndex = getNotifyPlayListIndex(playItems, mCurPlayItem.getAudioId());
                if (--needPlayIndex < 0) {
                    loadPrePage(iPlayListGetListener);
                    return;
                }
                break;
            default:
                break;
        }
        if (type == LOAD_DATA_PRE) {
            PlayItem playItem = getCurPlayItem();
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.addAll(0, playItems);
            setCurPosition(playItem);
        } else if (type == LOAD_DATA_PAGE || type == LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM || type == LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM) {
            PlayItem playItem = getPlayItem(mCurPlayItem, playItems);
            PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "current position = " + getCurPosition());
            mPlayItemArrayList.clear();
            mPlayItemArrayList.addAll(playItems);
            setCurPosition(playItem);
        } else {
            List<PlayItem> diffItems = getDiffList(mPlayItemArrayList, playItems);
            mPlayItemArrayList.addAll(diffItems);
        }

        PlayerLogUtil.log(getClass().getSimpleName(), "updatePlayListContent", "needPlayIndex = " + needPlayIndex);
        notifyPlayListGet(iPlayListGetListener, playItems.get(needPlayIndex), playItems);
        notifyPlayListChange(playItems);
    }

    private PlayItem getPlayItem(PlayItem playItem, List<PlayItem> playItemList) {
        if (playItem == null) {
            return null;
        }
        PlayItem item = null;
        for (int i = 0; i < playItemList.size(); i++) {
            if (playItem.getAudioId() == playItemList.get(i).getAudioId()) {
                item = playItemList.get(i);
                break;
            }
        }

        return item;
    }

    /**
     * 去重
     * 资讯内容更新比较即时，当拉取下一页的时候可能更新的多条内容，导致拉取重复数据，播放列表显示异常
     */
    public static List<PlayItem> getDiffList(List<PlayItem> fromList,
                                                      List<PlayItem> onList) {
        List<PlayItem> result = new ArrayList<>();
        for (PlayItem bean : onList) {
            boolean hasValue = false;
            for (PlayItem item : fromList) {
                if (bean.getAudioId() == item.getAudioId()) {
                    hasValue = true;
                    break;
                }
            }
            if (!hasValue) {
                result.add(bean);
            }
        }
        return result;
    }

    public static boolean isInList(List<PlayItem> fromList,
                                             PlayItem playItem) {
        boolean hasValue = false;
        if (playItem == null) {
            return false;
        }
        for (PlayItem item : fromList) {
            if (playItem.getAudioId() == item.getAudioId()) {
                hasValue = true;
                break;
            }
        }

        return hasValue;
    }

    private void updatePlayListInfo(int type, BasePageResult basePageResult) {
        updatePlayListInfo(type, null, basePageResult);
    }

    private void updatePlayListInfo(int type, AlbumDetails albumDetails, BasePageResult basePageResult) {
        switch (type) {
            case LOAD_DATA: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_PRE: {
                mPlaylistInfo.setPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_NEXT: {
                mPlaylistInfo.setNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
            }
            break;
            case LOAD_DATA_PAGE_AND_PLAY_NEXT_ITEM:
            case LOAD_DATA_PAGE_AND_PLAY_PRE_ITEM:
            case LOAD_DATA_PAGE:
                mPlaylistInfo.resetNextPage(basePageResult.getNextPage());
                mPlaylistInfo.setHasNextPage(basePageResult.getHaveNext() == PlayerConstants.HAVE_PAGE_DATA);
                mPlaylistInfo.resetPrePage(basePageResult.getPrePage());
                mPlaylistInfo.setHasPrePage(basePageResult.getHavePre() == PlayerConstants.HAVE_PAGE_DATA);
                break;
            default:
                break;
        }

        if (!StringUtil.isEmpty(mPlaylistInfo.getTempId())) {
            mPlaylistInfo.setId(mPlaylistInfo.getTempId());
        }
        mPlaylistInfo.setChildId(null);
        mPlaylistInfo.setPageIndex(String.valueOf(basePageResult.getNextPage()));
        mPlaylistInfo.setAllSize(basePageResult.getCount());

        if (albumDetails != null) {
            mPlaylistInfo.setAlbumName(albumDetails.getName());
            mPlaylistInfo.setAlbumPic(albumDetails.getImg());
            mPlaylistInfo.setCountNum(albumDetails.getCountNum());
            mPlaylistInfo.setFollowedNum(albumDetails.getFollowedNum());
            mPlaylistInfo.setListenNum(albumDetails.getListenNum());
            mPlaylistInfo.setSourceName(albumDetails.getSourceName());
            mPlaylistInfo.setSourceLogo(albumDetails.getSourceLogo());
            mPlaylistInfo.setBreakPointContinue(albumDetails.getBreakPointContinue());
        }
    }

    private void setPageSize(int pageSize){
        mPageSize = pageSize;
    }
}
