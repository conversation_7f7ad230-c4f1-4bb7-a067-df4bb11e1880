package com.kaolafm.report.util;

import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.ReportBean;
import com.kaolafm.report.model.ReportTask;


/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportUploadTask {
    private ReportBean mReportBean;

    public ReportUploadTask(ReportBean reportBean) {
        mReportBean = reportBean;
    }

    public void report() {
        ReportNetworkHelper.getInstance().request(mReportBean.getReportValue(), isOk -> {
            if (isOk) {
                disposeSuccessResult();
            } else {
                disposeErrorResult();
            }
        });
    }

    public void disposeErrorResult() {
        if (mReportBean.getType() == ReportConstants.UPLOAD_TASK_TYPE_NORMAL) {
            Logging.i(ReportConstants.REPORT_TAG, "发送立即上报数据失败. 执行插入数据 :" + mReportBean.getReportValue());
            ReportTask reportTask = new ReportTask();
            reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
            reportTask.setSingleTask(ReportDBHelper.getInstance().insertData(mReportBean.getReportValue()));
            ReportTaskHelper.getInstance().insertTask(reportTask);
        } else {
            ReportTaskHelper.getInstance().taskDone();
        }
    }

    public void disposeSuccessResult() {
        if (mReportBean.getType() != ReportConstants.UPLOAD_TASK_TYPE_BY_DATA_BASE) {
            Logging.i(ReportConstants.REPORT_TAG, "发送立即上报数据成功");
            return;
        }

        ReportTask reportTask = new ReportTask();
        reportTask.setType(ReportConstants.TASK_TYPE_DELETE);
        reportTask.setSingleTask(ReportDBHelper.getInstance().deleteDataList(mReportBean.getIdList()));
        ReportTaskHelper.getInstance().insertTask(reportTask);
        ReportTaskHelper.getInstance().taskDone();
    }


}
