package com.kaolafm.report.util;

import com.kaolafm.opensdk.log.Logging;
import com.kaolafm.report.model.ReportBeanBigData;
import com.kaolafm.report.model.ReportTask;


/**
 * <AUTHOR> on 2019/1/10.
 */

public class ReportUploadTaskBigData {
    private ReportBeanBigData mReportBean;

    public ReportUploadTaskBigData(ReportBeanBigData reportBean) {
        mReportBean = reportBean;
    }

    public void report() {
        ReportNetworkHelperBigData.getInstance().request(mReportBean.getReportJson(), isOk -> {
            if (isOk) {
                disposeSuccessResult();
            } else {
                disposeErrorResult();
            }
        });
    }

    public void disposeErrorResult() {
        if (mReportBean.getType() == ReportConstants.UPLOAD_TASK_TYPE_NORMAL) {
            Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送立即上报数据失败. 执行插入数据 :" + mReportBean.getReportJson());
            ReportTask reportTask = new ReportTask();
            reportTask.setType(ReportConstants.TASK_TYPE_INSERT);
            reportTask.setSingleTask(ReportDBHelperBigData.getInstance().insertData(mReportBean.getReportJson()));
            ReportTaskHelperBigData.getInstance().insertTask(reportTask);
        } else {
            ReportTaskHelperBigData.getInstance().taskDone();
        }
    }

    public void disposeSuccessResult() {
        if (mReportBean.getType() != ReportConstants.UPLOAD_TASK_TYPE_BY_DATA_BASE) {
            Logging.i(ReportConstants.REPORT_BIGDATA_TAG, "发送立即上报数据成功");
            return;
        }

        ReportTask reportTask = new ReportTask();
        reportTask.setType(ReportConstants.TASK_TYPE_DELETE);
        reportTask.setSingleTask(ReportDBHelperBigData.getInstance().deleteData(mReportBean.getId()));
        ReportTaskHelperBigData.getInstance().insertTask(reportTask);
        ReportTaskHelperBigData.getInstance().taskDone();
    }


}
